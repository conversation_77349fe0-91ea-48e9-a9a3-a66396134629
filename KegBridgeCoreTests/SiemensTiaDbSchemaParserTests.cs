using KegBridgeCore.Services.BinaryCoder;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Parlot.Fluent;

namespace KegBridgeCoreTests
{
    /// <summary>
    /// Tests the actual parser components directly from SiemensTiaDbSchemaSimpleParser
    /// This is safer than replicating components in test code
    /// </summary>
    public class SiemensTiaDbSchemaParserTests
    {
        [SetUp]
        public void Setup()
        {
        }

        private T ParseSafely<T>(Parser<T> parser, string input, string testName)
        {
            try
            {
                var result = parser.Parse(SiemensTiaDbSchemaParser.Normalize(input));
                Console.WriteLine($"{testName}: Success=True, Result='{result}'");
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{testName}: Failed - {ex.Message}");
                throw new AssertionException($"Failed to parse '{input}' in {testName}: {ex.Message}");
            }
        }

        [Test]
        public void Test_Identifier_Component()
        {
            var result1 = ParseSafely(SiemensTiaDbSchemaParser.Identifier, "Status", "Basic Identifier");
            Assert.That(result1, Is.EqualTo("Status"));
            
            var result2 = ParseSafely(SiemensTiaDbSchemaParser.Identifier, "_start", "Underscore Identifier");
            Assert.That(result2, Is.EqualTo("_start"));
        }

        [Test]
        public void Test_QuotedIdentifier_Component()
        {
            var result1 = ParseSafely(SiemensTiaDbSchemaParser.QuotedIdentifier, "\"UDT_KB_CE\"", "Quoted Identifier");
            Assert.That(result1, Is.EqualTo("UDT_KB_CE"));
            
            var result2 = ParseSafely(SiemensTiaDbSchemaParser.QuotedIdentifier, "\"Counter\"", "Quoted Counter");
            Assert.That(result2, Is.EqualTo("Counter"));
        }

        [Test]
        public void Test_AnyIdentifier_Component()
        {
            var result1 = ParseSafely(SiemensTiaDbSchemaParser.AnyIdentifier, "Status", "Any Identifier - Plain");
            Assert.That(result1, Is.EqualTo("Status"));
            
            var result2 = ParseSafely(SiemensTiaDbSchemaParser.AnyIdentifier, "\"Counter\"", "Any Identifier - Quoted");
            Assert.That(result2, Is.EqualTo("Counter"));
        }

        [Test]
        public void Test_SimpleType_Component()
        {
            var result1 = ParseSafely(SiemensTiaDbSchemaParser.SimpleType, "Byte", "Simple Type - Byte");
            Assert.That(result1.Name, Is.EqualTo("Byte"));
            
            var result2 = ParseSafely(SiemensTiaDbSchemaParser.SimpleType, "Real", "Simple Type - Real");
            Assert.That(result2.Name, Is.EqualTo("Real"));
            
            var result3 = ParseSafely(SiemensTiaDbSchemaParser.SimpleType, "\"UDT_Custom\"", "Simple Type - Quoted");
            Assert.That(result3.Name, Is.EqualTo("UDT_Custom"));
        }

        [Test]
        public void Test_ArrayRange_Component()
        {
            var result1 = ParseSafely(SiemensTiaDbSchemaParser.ArrayRange, "[0..100]", "Array Range 0-100");
            Assert.That(result1, Is.EqualTo(101));
            
            var result2 = ParseSafely(SiemensTiaDbSchemaParser.ArrayRange, "[1..5]", "Array Range 1-5");
            Assert.That(result2, Is.EqualTo(5));
            
            var result3 = ParseSafely(SiemensTiaDbSchemaParser.ArrayRange, "[10..20]", "Array Range 10-20");
            Assert.That(result3, Is.EqualTo(11));
        }

        [Test]
        public void Test_ArrayType_Component()
        {
            var result1 = ParseSafely(SiemensTiaDbSchemaParser.ArrayType, "Array[0..100] of Byte", "Array Type - Byte");
            Assert.That(result1.Size, Is.EqualTo(101));
            Assert.That(result1.Type, Is.TypeOf<BinaryType>());
            Assert.That(((BinaryType)result1.Type).Name, Is.EqualTo("Byte"));
            
            var result2 = ParseSafely(SiemensTiaDbSchemaParser.ArrayType, "Array[1..5] of Int", "Array Type - Int");
            Assert.That(result2.Size, Is.EqualTo(5));
            Assert.That(((BinaryType)result2.Type).Name, Is.EqualTo("Int"));
        }

        [Test]
        public void Test_Property_Component()
        {
            var result1 = ParseSafely(SiemensTiaDbSchemaParser.Property, "Status : Byte;", "Simple Property");
            Assert.That(result1.Name, Is.EqualTo("Status"));
            Assert.That(result1.Type, Is.TypeOf<BinaryType>());
            Assert.That(((BinaryType)result1.Type).Name, Is.EqualTo("Byte"));

            var result2 = ParseSafely(SiemensTiaDbSchemaParser.Property, "\"Counter\" : DInt;", "Quoted Property");
            Assert.That(result2.Name, Is.EqualTo("Counter"));
            Assert.That(((BinaryType)result2.Type).Name, Is.EqualTo("DInt"));

            var result3 = ParseSafely(SiemensTiaDbSchemaParser.Property, "Data : Array[0..100] of Byte;", "Array Property");
            Assert.That(result3.Name, Is.EqualTo("Data"));
            Assert.That(result3.Type, Is.TypeOf<BinaryArray>());
            var arrayType = (BinaryArray)result3.Type;
            Assert.That(arrayType.Size, Is.EqualTo(101));
            Assert.That(((BinaryType)arrayType.Type).Name, Is.EqualTo("Byte"));
        }

        [Test]
        public void Test_Property_WithAttributes()
        {
            // Test property with attributes in curly braces
            var result1 = ParseSafely(SiemensTiaDbSchemaParser.Property, "Status {InstructionName := 'Status'} : Byte;", "Property with attributes");
            Assert.That(result1.Name, Is.EqualTo("Status"));
            Assert.That(result1.Type, Is.TypeOf<BinaryType>());
            Assert.That(((BinaryType)result1.Type).Name, Is.EqualTo("Byte"));

            var result2 = ParseSafely(SiemensTiaDbSchemaParser.Property, "\"Counter\" {S7_Visible := 'false'; InstructionName := 'Counter'} : DInt;", "Property with multiple attributes");
            Assert.That(result2.Name, Is.EqualTo("Counter"));
            Assert.That(((BinaryType)result2.Type).Name, Is.EqualTo("DInt"));

            // Test property without attributes (should still work)
            var result3 = ParseSafely(SiemensTiaDbSchemaParser.Property, "Value : Real;", "Property without attributes");
            Assert.That(result3.Name, Is.EqualTo("Value"));
            Assert.That(((BinaryType)result3.Type).Name, Is.EqualTo("Real"));
        }

        [Test]
        public void Test_Property_WithComments()
        {
            // Test property with comment after semicolon
            var result1 = ParseSafely(SiemensTiaDbSchemaParser.Property, "Action : Int;   // 0:=Not active/1:=Active Empty", "Property with comment");
            Assert.That(result1.Name, Is.EqualTo("Action"));
            Assert.That(result1.Type, Is.TypeOf<BinaryType>());
            Assert.That(((BinaryType)result1.Type).Name, Is.EqualTo("Int"));

            var result2 = ParseSafely(SiemensTiaDbSchemaParser.Property, "Status : Int; // 0:=Ready/1:=Executing/2:=Hand/3:=Warning/4:=Error", "Property with long comment");
            Assert.That(result2.Name, Is.EqualTo("Status"));
            Assert.That(((BinaryType)result2.Type).Name, Is.EqualTo("Int"));

            // Test property without comment (should still work)
            var result3 = ParseSafely(SiemensTiaDbSchemaParser.Property, "Value : Real;", "Property without comment");
            Assert.That(result3.Name, Is.EqualTo("Value"));
            Assert.That(((BinaryType)result3.Type).Name, Is.EqualTo("Real"));
        }

        [Test]
        public void Test_Property_WithComments_FullConsumption()
        {
            // Test if parser consumes all characters including comments using PropertyComplete
            var result1 = ParseSafely(SiemensTiaDbSchemaParser.PropertyComplete, "Action : Int;   // 0:=Not active/1:=Active Empty", "Property with comment - complete consumption");
            Assert.That(result1.Name, Is.EqualTo("Action"));
            Assert.That(result1.Type, Is.TypeOf<BinaryType>());
            Assert.That(((BinaryType)result1.Type).Name, Is.EqualTo("Int"));

            // Test property without comment (should consume everything)
            var result2 = ParseSafely(SiemensTiaDbSchemaParser.PropertyComplete, "Value : Real;", "Property without comment - complete consumption");
            Assert.That(result2.Name, Is.EqualTo("Value"));
            Assert.That(((BinaryType)result2.Type).Name, Is.EqualTo("Real"));

            // Test that regular Property parser does NOT consume comments (should fail with PropertyComplete)
            try
            {
                var result3 = ParseSafely(SiemensTiaDbSchemaParser.PropertyComplete, "Status : Int; // This should fail if comments aren't consumed", "Property with comment - should show if comments are consumed");
                Console.WriteLine("PropertyComplete succeeded - comments are being consumed!");
            }
            catch
            {
                Console.WriteLine("PropertyComplete failed - comments are NOT being consumed by Property parser");
            }
        }



        [Test]
        public void Test_StructType_Component()
        {
            var result1 = ParseSafely(SiemensTiaDbSchemaParser.StructType, "STRUCT Status : Byte; Value : Real; END_STRUCT", "Simple Struct");
            Assert.That(result1.Properties.Count, Is.EqualTo(2));
            Assert.That(result1.Properties[0].Name, Is.EqualTo("Status"));
            Assert.That(result1.Properties[1].Name, Is.EqualTo("Value"));
            Assert.That(((BinaryType)result1.Properties[0].Type).Name, Is.EqualTo("Byte"));
            Assert.That(((BinaryType)result1.Properties[1].Type).Name, Is.EqualTo("Real"));
        }

        [Test]
        public void Test_TypeDefinition_Component()
        {
            var result1 = ParseSafely(SiemensTiaDbSchemaParser.TypeDefinition, "TYPE \"UDT_Test\" STRUCT Status : Byte; Value : Real; END_STRUCT END_TYPE", "Simple TYPE");
            Assert.That(result1.Name, Is.EqualTo("UDT_Test"));
            Assert.That(result1.Properties.Count, Is.EqualTo(2));
            Assert.That(result1.Properties[0].Name, Is.EqualTo("Status"));
            Assert.That(result1.Properties[1].Name, Is.EqualTo("Value"));

            var result2 = ParseSafely(SiemensTiaDbSchemaParser.TypeDefinition, "TYPE UDT_Simple STRUCT Counter : DInt; END_STRUCT END_TYPE", "Unquoted TYPE");
            Assert.That(result2.Name, Is.EqualTo("UDT_Simple"));
            Assert.That(result2.Properties.Count, Is.EqualTo(1));
            Assert.That(result2.Properties[0].Name, Is.EqualTo("Counter"));
        }

        [Test]
        public void Test_TypeDefinition_WithVersionAndMetadata()
        {
            // Test that VERSION lines and other metadata are ignored
            var typeWithVersion = @"TYPE ""UDT_KB_CM_AI""
VERSION : 0.1
NON_RETAIN
{InstructionName := 'UDT_KB_CM_AI'; LibVersion := '1.0'}
// Comment
STRUCT
   Status : Byte;
   Value : Real;
END_STRUCT;
END_TYPE";

            var result = ParseSafely(SiemensTiaDbSchemaParser.TypeDefinition, typeWithVersion, "TYPE with VERSION and metadata");
            Assert.That(result.Name, Is.EqualTo("UDT_KB_CM_AI"));
            Assert.That(result.Properties.Count, Is.EqualTo(2));
            Assert.That(result.Properties[0].Name, Is.EqualTo("Status"));
            Assert.That(result.Properties[1].Name, Is.EqualTo("Value"));

            // Test with single line metadata
            var typeWithSingleLineMetadata = "TYPE \"UDT_Simple\" VERSION : 0.1 NON_RETAIN STRUCT Counter : DInt; END_STRUCT END_TYPE";
            var result2 = ParseSafely(SiemensTiaDbSchemaParser.TypeDefinition, typeWithSingleLineMetadata, "TYPE with single line metadata");
            Assert.That(result2.Name, Is.EqualTo("UDT_Simple"));
            Assert.That(result2.Properties.Count, Is.EqualTo(1));
            Assert.That(result2.Properties[0].Name, Is.EqualTo("Counter"));
        }

        [Test]
        public void Test_TypeDefinition_WithTrailingMetadata()
        {
            // Test that metadata between END_STRUCT and END_TYPE is ignored
            var typeWithTrailingMetadata = @"TYPE ""UDT_Test""
STRUCT
   Status : Byte;
   Value : Real;
END_STRUCT;
{S7_Optimized_Access := 'TRUE'}
NON_RETAIN
END_TYPE";

            var result = ParseSafely(SiemensTiaDbSchemaParser.TypeDefinition, typeWithTrailingMetadata, "TYPE with trailing metadata");
            Assert.That(result.Name, Is.EqualTo("UDT_Test"));
            Assert.That(result.Properties.Count, Is.EqualTo(2));
            Assert.That(result.Properties[0].Name, Is.EqualTo("Status"));
            Assert.That(result.Properties[1].Name, Is.EqualTo("Value"));

            // Test with both leading and trailing metadata
            var typeWithBothMetadata = @"TYPE ""UDT_Complex""
VERSION : 0.1
{InstructionName := 'UDT_Complex'}
STRUCT
   Counter : DInt;
END_STRUCT;
{S7_Optimized_Access := 'TRUE'}
NON_RETAIN
{LibVersion := '1.0'}
END_TYPE";

            var result2 = ParseSafely(SiemensTiaDbSchemaParser.TypeDefinition, typeWithBothMetadata, "TYPE with both leading and trailing metadata");
            Assert.That(result2.Name, Is.EqualTo("UDT_Complex"));
            Assert.That(result2.Properties.Count, Is.EqualTo(1));
            Assert.That(result2.Properties[0].Name, Is.EqualTo("Counter"));
        }

        [Test]
        public void Test_ParseWithNormalizedLineEndings()
        {
            // Test with Windows line endings (\r\n) - focus on line ending normalization
            var typeWithWindowsLineEndings = "TYPE \"UDT_Test\"\r\nVERSION : 0.1\r\n   STRUCT\r\n      Action : Int;\r\n      Status : Int;\r\n      Value : Real;\r\n   END_STRUCT;\r\n\r\nEND_TYPE";

            var result = SiemensTiaDbSchemaParser.Parse(typeWithWindowsLineEndings);

            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result[0].Name, Is.EqualTo("UDT_Test"));
            Assert.That(result[0].Properties.Count, Is.EqualTo(3));
            Assert.That(result[0].Properties[0].Name, Is.EqualTo("Action"));
            Assert.That(result[0].Properties[1].Name, Is.EqualTo("Status"));
            Assert.That(result[0].Properties[2].Name, Is.EqualTo("Value"));

            // Test with Unix line endings (\n)
            var typeWithUnixLineEndings = "TYPE \"UDT_Test2\"\nVERSION : 0.1\n   STRUCT\n      Counter : DInt;\n      Flag : Bool;\n   END_STRUCT;\n\nEND_TYPE";

            var result2 = SiemensTiaDbSchemaParser.Parse(typeWithUnixLineEndings);

            Assert.That(result2.Count, Is.EqualTo(1));
            Assert.That(result2[0].Name, Is.EqualTo("UDT_Test2"));
            Assert.That(result2[0].Properties.Count, Is.EqualTo(2));
            Assert.That(result2[0].Properties[0].Name, Is.EqualTo("Counter"));
            Assert.That(result2[0].Properties[1].Name, Is.EqualTo("Flag"));

            // Test that both approaches give the same result
            var normalizedWindows = typeWithWindowsLineEndings.Replace("\r\n", "\n");
            var directResult = SiemensTiaDbSchemaParser.Parser.Parse(normalizedWindows);

            Assert.That(directResult.Count, Is.EqualTo(result.Count));
            Assert.That(directResult[0].Name, Is.EqualTo(result[0].Name));
            Assert.That(directResult[0].Properties.Count, Is.EqualTo(result[0].Properties.Count));
        }

        [Test]
        public void Test_TypeDefinition_WithPropertyComments()
        {
            // Test with a simple TYPE definition with comments
            var typeWithoutComments = @"TYPE ""UDT_Simple""
   STRUCT
      Action : Int;
      Status : Int;
      Value : Real;
   END_STRUCT;
END_TYPE";

            var result = SiemensTiaDbSchemaParser.Parse(typeWithoutComments);

            Console.WriteLine($"Parse result count: {result.Count}");
            if (result.Count > 0)
            {
                Console.WriteLine($"First result name: '{result[0].Name}'");
                Console.WriteLine($"First result properties: {result[0].Properties.Count}");
            }

            Assert.That(result.Count, Is.EqualTo(1), "Should parse one TYPE definition");
            Assert.That(result[0].Name, Is.EqualTo("UDT_Simple"));
            Assert.That(result[0].Properties.Count, Is.EqualTo(3), "Should have 3 properties");
            Assert.That(result[0].Properties[0].Name, Is.EqualTo("Action"));
            Assert.That(result[0].Properties[1].Name, Is.EqualTo("Status"));
            Assert.That(result[0].Properties[2].Name, Is.EqualTo("Value"));
            Assert.That(((BinaryType)result[0].Properties[0].Type).Name, Is.EqualTo("Int"));
            Assert.That(((BinaryType)result[0].Properties[1].Type).Name, Is.EqualTo("Int"));
            Assert.That(((BinaryType)result[0].Properties[2].Type).Name, Is.EqualTo("Real"));
        }

        [Test]
        public void Test_DataBlockDefinition_Component()
        {
            // Test simple DATA_BLOCK
            var result1 = ParseSafely(SiemensTiaDbSchemaParser.DataBlockDefinition, "DATA_BLOCK \"DB_Test\" STRUCT Status : Byte; Value : Real; END_STRUCT END_DATA_BLOCK", "Simple DATA_BLOCK");
            Assert.That(result1.Name, Is.EqualTo("DB_Test"));
            Assert.That(result1.Properties.Count, Is.EqualTo(2));
            Assert.That(result1.Properties[0].Name, Is.EqualTo("Status"));
            Assert.That(result1.Properties[1].Name, Is.EqualTo("Value"));

            // Test unquoted DATA_BLOCK name
            var result2 = ParseSafely(SiemensTiaDbSchemaParser.DataBlockDefinition, "DATA_BLOCK DB_Simple STRUCT Counter : DInt; END_STRUCT END_DATA_BLOCK", "Unquoted DATA_BLOCK");
            Assert.That(result2.Name, Is.EqualTo("DB_Simple"));
            Assert.That(result2.Properties.Count, Is.EqualTo(1));
            Assert.That(result2.Properties[0].Name, Is.EqualTo("Counter"));
        }

        [Test]
        public void Test_DataBlockDefinition_WithMetadata()
        {
            // Test DATA_BLOCK with leading metadata
            var dataBlockWithMetadata = @"DATA_BLOCK ""DB_Test""
VERSION : 0.1
NON_RETAIN
{InstructionName := 'DB_Test'; LibVersion := '1.0'}
STRUCT
   Status : Byte;
   Value : Real;
END_STRUCT;
END_DATA_BLOCK";

            var result = ParseSafely(SiemensTiaDbSchemaParser.DataBlockDefinition, dataBlockWithMetadata, "DATA_BLOCK with metadata");
            Assert.That(result.Name, Is.EqualTo("DB_Test"));
            Assert.That(result.Properties.Count, Is.EqualTo(2));
            Assert.That(result.Properties[0].Name, Is.EqualTo("Status"));
            Assert.That(result.Properties[1].Name, Is.EqualTo("Value"));

            // Test DATA_BLOCK with trailing metadata
            var dataBlockWithTrailingMetadata = @"DATA_BLOCK ""DB_Complex""
STRUCT
   Counter : DInt;
   Data : Array[0..100] of Byte;
END_STRUCT;
{S7_Optimized_Access := 'TRUE'}
NON_RETAIN
END_DATA_BLOCK";

            var result2 = ParseSafely(SiemensTiaDbSchemaParser.DataBlockDefinition, dataBlockWithTrailingMetadata, "DATA_BLOCK with trailing metadata");
            Assert.That(result2.Name, Is.EqualTo("DB_Complex"));
            Assert.That(result2.Properties.Count, Is.EqualTo(2));
            Assert.That(result2.Properties[0].Name, Is.EqualTo("Counter"));
            Assert.That(result2.Properties[1].Name, Is.EqualTo("Data"));
            Assert.That(result2.Properties[1].Type, Is.TypeOf<BinaryArray>());
        }

        [Test]
        public void Test_FullParser_Component()
        {
            var schema = @"
                TYPE ""UDT_KB_CM_AI"" STRUCT Status : Byte; Value : Real; END_STRUCT END_TYPE
                TYPE ""UDT_KB_CM_DI"" STRUCT Status : Byte; RawInput : Bool; END_STRUCT END_TYPE
            ";

            var result = ParseSafely(SiemensTiaDbSchemaParser.Parser, schema, "Full Parser - Multiple Types");
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].Name, Is.EqualTo("UDT_KB_CM_AI"));
            Assert.That(result[1].Name, Is.EqualTo("UDT_KB_CM_DI"));
        }

        [Test]
        public void Test_FullParser_WithMixedDefinitions()
        {
            var schema = @"
                TYPE ""UDT_Test"" STRUCT Status : Byte; Value : Real; END_STRUCT END_TYPE
                DATA_BLOCK ""DB_Test"" STRUCT Counter : DInt; Data : Array[0..10] of Byte; END_STRUCT END_DATA_BLOCK
                TYPE ""UDT_Another"" STRUCT Flag : Bool; END_STRUCT END_TYPE
                DATA_BLOCK ""DB_Another"" STRUCT Temperature : Real; END_STRUCT END_DATA_BLOCK
            ";

            var result = ParseSafely(SiemensTiaDbSchemaParser.Parser, schema, "Full Parser - Mixed TYPE and DATA_BLOCK");
            Assert.That(result.Count, Is.EqualTo(4));

            // Check TYPE definitions
            Assert.That(result[0].Name, Is.EqualTo("UDT_Test"));
            Assert.That(result[0].Properties.Count, Is.EqualTo(2));
            Assert.That(result[2].Name, Is.EqualTo("UDT_Another"));
            Assert.That(result[2].Properties.Count, Is.EqualTo(1));

            // Check DATA_BLOCK definitions
            Assert.That(result[1].Name, Is.EqualTo("DB_Test"));
            Assert.That(result[1].Properties.Count, Is.EqualTo(2));
            Assert.That(result[1].Properties[1].Type, Is.TypeOf<BinaryArray>());
            Assert.That(result[3].Name, Is.EqualTo("DB_Another"));
            Assert.That(result[3].Properties.Count, Is.EqualTo(1));
        }

        [Test]
        public void Test_NestedStruct_Property()
        {
            // This tests that nested structs work in properties
            var nestedProperty = "Seq : Struct Msg_ID : Int; Val : Real; END_STRUCT;";
            
            var result = ParseSafely(SiemensTiaDbSchemaParser.Property, nestedProperty, "Nested Struct Property");
            Assert.That(result.Name, Is.EqualTo("Seq"));
            Assert.That(result.Type, Is.TypeOf<BinaryStruct>());
            
            var nestedStruct = (BinaryStruct)result.Type;
            Assert.That(nestedStruct.Properties.Count, Is.EqualTo(2));
            Assert.That(nestedStruct.Properties[0].Name, Is.EqualTo("Msg_ID"));
            Assert.That(nestedStruct.Properties[1].Name, Is.EqualTo("Val"));
        }

        [Test]
        public void Test_ComplexTypeDefinition()
        {
            var complexType = @"TYPE ""UDT_Complex"" STRUCT 
                ""Counter"" : DInt; 
                Seq : Struct 
                    Msg_ID : Int; 
                    Val : Real; 
                END_STRUCT; 
                Data : Array[0..100] of Byte; 
                END_STRUCT END_TYPE";

            var result = ParseSafely(SiemensTiaDbSchemaParser.TypeDefinition, complexType, "Complex TYPE");
            Assert.That(result.Name, Is.EqualTo("UDT_Complex"));
            Assert.That(result.Properties.Count, Is.EqualTo(3));
            
            // Check Counter property
            Assert.That(result.Properties[0].Name, Is.EqualTo("Counter"));
            Assert.That(result.Properties[0].Type, Is.TypeOf<BinaryType>());
            
            // Check Seq nested struct
            Assert.That(result.Properties[1].Name, Is.EqualTo("Seq"));
            Assert.That(result.Properties[1].Type, Is.TypeOf<BinaryStruct>());
            
            // Check Data array
            Assert.That(result.Properties[2].Name, Is.EqualTo("Data"));
            Assert.That(result.Properties[2].Type, Is.TypeOf<BinaryArray>());
        }

        [Test]
        public void Test_ComplexDbFile_ShouldParseCorrectly()
        {
            // Read the actual Siemens DB file
            var dbFilePath = Path.Combine(TestContext.CurrentContext.TestDirectory, "siemens_db_complex.db");
            Assert.That(File.Exists(dbFilePath), Is.True, $"Test file not found: {dbFilePath}");

            var dbContent = File.ReadAllText(dbFilePath);

            var result = ParseSafely(SiemensTiaDbSchemaParser.Parser, dbContent, "Very Complex TYPE");
            Assert.That(result.Count, Is.EqualTo(10), "Should parse 6 TYPE definitions and 4 DATA_BLOCK definitions");

            // Verify TYPE definitions (first 6 results)

            // 1. UDT_KB_CE - Complex type with nested structs and comments
            Assert.That(result[0].Name, Is.EqualTo("UDT_KB_CE"));
            Assert.That(result[0].Properties.Count, Is.EqualTo(3), "UDT_KB_CE should have 3 properties");
            Assert.That(result[0].Properties[0].Name, Is.EqualTo("Counter"));
            Assert.That(result[0].Properties[0].Type, Is.TypeOf<BinaryType>());
            Assert.That(((BinaryType)result[0].Properties[0].Type).Name, Is.EqualTo("DInt"));

            // Check nested Seq struct
            Assert.That(result[0].Properties[1].Name, Is.EqualTo("Seq"));
            Assert.That(result[0].Properties[1].Type, Is.TypeOf<BinaryStruct>());
            var seqStruct = (BinaryStruct)result[0].Properties[1].Type;
            Assert.That(seqStruct.Properties.Count, Is.EqualTo(6), "Seq struct should have 6 properties");
            Assert.That(seqStruct.Properties[0].Name, Is.EqualTo("Msg_ID"));
            Assert.That(seqStruct.Properties[1].Name, Is.EqualTo("Msg_Step"));
            Assert.That(seqStruct.Properties[2].Name, Is.EqualTo("Msg_Stat"));
            Assert.That(seqStruct.Properties[3].Name, Is.EqualTo("KegID"));
            Assert.That(seqStruct.Properties[4].Name, Is.EqualTo("ActiveStep"));
            Assert.That(seqStruct.Properties[5].Name, Is.EqualTo("Val"));

            // Check nested CE_Status struct with comments
            Assert.That(result[0].Properties[2].Name, Is.EqualTo("CE_Status"));
            Assert.That(result[0].Properties[2].Type, Is.TypeOf<BinaryStruct>());
            var ceStatusStruct = (BinaryStruct)result[0].Properties[2].Type;
            Assert.That(ceStatusStruct.Properties.Count, Is.EqualTo(2), "CE_Status struct should have 2 properties");
            Assert.That(ceStatusStruct.Properties[0].Name, Is.EqualTo("Action"));
            Assert.That(ceStatusStruct.Properties[1].Name, Is.EqualTo("Status"));

            // 2. UDT_KB_CM_FT - Flow transmitter type
            Assert.That(result[1].Name, Is.EqualTo("UDT_KB_CM_FT"));
            Assert.That(result[1].Properties.Count, Is.EqualTo(10), "UDT_KB_CM_FT should have 10 properties");
            Assert.That(result[1].Properties[0].Name, Is.EqualTo("Status"));
            Assert.That(result[1].Properties[1].Name, Is.EqualTo("In_Pre"));
            Assert.That(result[1].Properties[9].Name, Is.EqualTo("Total"));

            // 3. UDT_KB_CM_AI - Analog input type
            Assert.That(result[2].Name, Is.EqualTo("UDT_KB_CM_AI"));
            Assert.That(result[2].Properties.Count, Is.EqualTo(2), "UDT_KB_CM_AI should have 2 properties");
            Assert.That(result[2].Properties[0].Name, Is.EqualTo("Status"));
            Assert.That(result[2].Properties[1].Name, Is.EqualTo("Value"));

            // 4. UDT_KB_CM_DI - Digital input type
            Assert.That(result[3].Name, Is.EqualTo("UDT_KB_CM_DI"));
            Assert.That(result[3].Properties.Count, Is.EqualTo(2), "UDT_KB_CM_DI should have 2 properties");
            Assert.That(result[3].Properties[0].Name, Is.EqualTo("Status"));
            Assert.That(result[3].Properties[1].Name, Is.EqualTo("RawInput"));

            // 5. UDT_KB_CM_MTR - Motor type
            Assert.That(result[4].Name, Is.EqualTo("UDT_KB_CM_MTR"));
            Assert.That(result[4].Properties.Count, Is.EqualTo(6), "UDT_KB_CM_MTR should have 6 properties");
            Assert.That(result[4].Properties[0].Name, Is.EqualTo("Status"));
            Assert.That(result[4].Properties[5].Name, Is.EqualTo("StatusWord"));
            Assert.That(((BinaryType)result[4].Properties[5].Type).Name, Is.EqualTo("Word"));

            // 6. UDT_KB_CM_DQ - Digital output type
            Assert.That(result[5].Name, Is.EqualTo("UDT_KB_CM_DQ"));
            Assert.That(result[5].Properties.Count, Is.EqualTo(5), "UDT_KB_CM_DQ should have 5 properties");
            Assert.That(result[5].Properties[0].Name, Is.EqualTo("Status"));
            Assert.That(result[5].Properties[4].Name, Is.EqualTo("FdbOpn"));

            // Verify DATA_BLOCK definitions (last 4 results)

            // 7. KegbridgeGeneral - Empty data block
            Assert.That(result[6].Name, Is.EqualTo("KegbridgeGeneral"));
            Assert.That(result[6].Properties.Count, Is.EqualTo(0), "KegbridgeGeneral should be empty");

            // 8. KegbridgeCE - Data block with UDT_KB_CE instances and comments
            Assert.That(result[7].Name, Is.EqualTo("KegbridgeCE"));
            Assert.That(result[7].Properties.Count, Is.EqualTo(6), "KegbridgeCE should have 6 properties");
            Assert.That(result[7].Properties[0].Name, Is.EqualTo("CE_T01_Ac"));
            Assert.That(result[7].Properties[1].Name, Is.EqualTo("CE_T01_Ca"));
            Assert.That(result[7].Properties[2].Name, Is.EqualTo("CE_T01_HW"));
            Assert.That(result[7].Properties[3].Name, Is.EqualTo("CE_T01_MW"));
            Assert.That(result[7].Properties[4].Name, Is.EqualTo("CE_FH"));
            Assert.That(result[7].Properties[5].Name, Is.EqualTo("CE_WH"));
            // All should reference UDT_KB_CE
            for (int i = 0; i < 6; i++)
            {
                Assert.That(result[7].Properties[i].Type, Is.TypeOf<BinaryType>());
                Assert.That(((BinaryType)result[7].Properties[i].Type).Name, Is.EqualTo("UDT_KB_CE"));
            }

            // 8. KegbridgeCM - Large data block with many CM instances
            Assert.That(result[8].Name, Is.EqualTo("KegbridgeCM"));
            Assert.That(result[8].Properties.Count, Is.EqualTo(54), "KegbridgeCM should have 54 properties");
            Assert.That(result[8].Properties[0].Name, Is.EqualTo("F1_H1_AI_1PT"));
            Assert.That(((BinaryType)result[8].Properties[0].Type).Name, Is.EqualTo("UDT_KB_CM_AI"));
            Assert.That(result[8].Properties[53].Name, Is.EqualTo("T01_MW_YP_W"));
            Assert.That(((BinaryType)result[8].Properties[53].Type).Name, Is.EqualTo("UDT_KB_CM_DQ"));

            // 9. DBTEST2 - Data block with nested struct and array
            Assert.That(result[9].Name, Is.EqualTo("DBTEST2"));
            Assert.That(result[9].Properties.Count, Is.EqualTo(1), "DBTEST2 should have 1 property");
            Assert.That(result[9].Properties[0].Name, Is.EqualTo("struct"));
            Assert.That(result[9].Properties[0].Type, Is.TypeOf<BinaryStruct>());
            var dbtest2Struct = (BinaryStruct)result[9].Properties[0].Type;
            Assert.That(dbtest2Struct.Properties.Count, Is.EqualTo(6), "DBTEST2 struct should have 6 properties");
            Assert.That(dbtest2Struct.Properties[0].Name, Is.EqualTo("_start"));
            Assert.That(dbtest2Struct.Properties[1].Name, Is.EqualTo("_db_id"));
            Assert.That(dbtest2Struct.Properties[2].Name, Is.EqualTo("_epoch_ns"));
            Assert.That(dbtest2Struct.Properties[3].Name, Is.EqualTo("ArData"));
            Assert.That(dbtest2Struct.Properties[4].Name, Is.EqualTo("Counter"));
            Assert.That(dbtest2Struct.Properties[5].Name, Is.EqualTo("_end"));

            // Check the array in DBTEST2
            Assert.That(dbtest2Struct.Properties[3].Type, Is.TypeOf<BinaryArray>());
            var arDataArray = (BinaryArray)dbtest2Struct.Properties[3].Type;
            Assert.That(arDataArray.Size, Is.EqualTo(53), "ArData array should have size 53 (0..52)");
            Assert.That(((BinaryType)arDataArray.Type).Name, Is.EqualTo("Byte"));
        }
    }
}
