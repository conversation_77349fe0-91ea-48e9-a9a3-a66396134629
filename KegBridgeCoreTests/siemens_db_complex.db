TYPE "UDT_KB_CE"
VERSION : 0.1
   STRUCT
      "Counter" : DInt;
      Seq : Struct
         Msg_ID : Int;
         Msg_Step : Int;
         Msg_Stat : Int;
         KegID : Int;
         ActiveStep : Int;
         Val : Real;
      END_STRUCT;
      CE_Status : Struct
         Action : Int;   // 0:=Not active/1:=Active Empty not running/2:= Active Empty/3:= Active not Empty running/4:=Active buffer modus/5:= Active Saturation modus/6:= Active Saturated
         Status : Int;   // 0:=Ready Not execting no error / No warning/1:= Ready executing/2:=Hand/3:=Warning/4:=Error
      END_STRUCT;
   END_STRUCT;

END_TYPE

TYPE "UDT_KB_CM_FT"
VERSION : 0.1
   STRUCT
      Status : Byte;
      In_Pre : Bool;
      In_End : Bool;
      In_Fault : Bool;
      In_Puls : Bool;
      Out_Bit0 : Bool;
      Out_Bit1 : Bool;
      Out_Start : Bool;
      Out_Stop : Bool;
      Flow : Int;
      Total : Int;
   END_STRUCT;

END_TYPE

TYPE "UDT_KB_CM_AI"
VERSION : 0.1
   STRUCT
      Status : Byte;
      Value : Real;
   END_STRUCT;

END_TYPE

TYPE "UDT_KB_CM_DI"
VERSION : 0.1
   STRUCT
      Status : Byte;
      RawInput : Bool;
   END_STRUCT;

END_TYPE

TYPE "UDT_KB_CM_MTR"
VERSION : 0.1
   STRUCT
      Status : Byte;
      ReqFwd : Bool;
      ReqBwd : Bool;
      ActCurrent : Int;
      SpSpeed : Int;
      StatusWord : Word;
   END_STRUCT;

END_TYPE

TYPE "UDT_KB_CM_DQ"
VERSION : 0.1
   STRUCT
      Status : Byte;
      OutCls : Bool;
      OutOpn : Bool;
      FdbCls : Bool;
      FdbOpn : Bool;
   END_STRUCT;

END_TYPE

DATA_BLOCK "KegbridgeGeneral"
{ S7_Optimized_Access := 'FALSE' }
VERSION : 0.1
NON_RETAIN

BEGIN

END_DATA_BLOCK

DATA_BLOCK "KegbridgeCE"
TITLE = KegBridgeCE title
{ S7_Optimized_Access := 'FALSE' }
AUTHOR : 'Bart Duchesne'
FAMILY : LambrechtsLib
NAME : UserDefinedId
VERSION : 0.1
NON_RETAIN
//Datablock comment 
   STRUCT 
      CE_T01_Ac { ExternalAccessible := 'False'; ExternalVisible := 'False'; ExternalWritable := 'False'; S7_SetPoint := 'False'} : "UDT_KB_CE";   // Ac comment
      CE_T01_Ca { ExternalAccessible := 'False'; ExternalVisible := 'False'; ExternalWritable := 'False'; S7_SetPoint := 'False'} : "UDT_KB_CE";   // Ca comment
      CE_T01_HW { ExternalAccessible := 'False'; ExternalVisible := 'False'; ExternalWritable := 'False'; S7_SetPoint := 'False'} : "UDT_KB_CE";
      CE_T01_MW { ExternalAccessible := 'False'; ExternalVisible := 'False'; ExternalWritable := 'False'; S7_SetPoint := 'False'} : "UDT_KB_CE";
      CE_FH : "UDT_KB_CE";
      CE_WH : "UDT_KB_CE";
   END_STRUCT;


BEGIN

END_DATA_BLOCK

DATA_BLOCK "KegbridgeCM"
{ S7_Optimized_Access := 'FALSE' }
VERSION : 0.1
NON_RETAIN
   STRUCT 
      F1_H1_AI_1PT : "UDT_KB_CM_AI";
      F1_H1_AI_1TT : "UDT_KB_CM_AI";
      F1_H1_AI_1CS : "UDT_KB_CM_AI";
      F1_H1_DI_1FS : "UDT_KB_CM_DI";
      F1_H1_AI_2PT : "UDT_KB_CM_AI";
      F1_H1_AI_2TT : "UDT_KB_CM_AI";
      F1_H1_AI_2CS : "UDT_KB_CM_AI";
      F1_H1_DI_2FS : "UDT_KB_CM_DI";
      F1_H1_PT_PV : "UDT_KB_CM_AI";
      F1_H2_AI_1PT : "UDT_KB_CM_AI";
      F1_H2_AI_1TT : "UDT_KB_CM_AI";
      F1_H2_AI_1CS : "UDT_KB_CM_AI";
      F1_H2_DI_1FS : "UDT_KB_CM_DI";
      F1_H2_AI_2PT : "UDT_KB_CM_AI";
      F1_H2_AI_2TT : "UDT_KB_CM_AI";
      F1_H2_AI_2CS : "UDT_KB_CM_AI";
      F1_H2_AI_PT_PV : "UDT_KB_CM_AI";
      MTR_FC302 : "UDT_KB_CM_MTR";
      MS1_A_FT_A : "UDT_KB_CM_FT";
      MS1_CO2_FT_1CO2 : "UDT_KB_CM_FT";
      MS1_CO2_FT_2CO2 : "UDT_KB_CM_FT";
      MS1_SA_FT_SA : "UDT_KB_CM_FT";
      MS1_SSt_FT_SSt : "UDT_KB_CM_FT";
      MS1_St_FT_ST : "UDT_KB_CM_FT";
      MS1_W_FT_W : "UDT_KB_CM_FT";
      T01_Ac_CT : "UDT_KB_CM_AI";
      T01_Ac_Heat : "UDT_KB_CM_DQ";
      T01_Ac_LSH : "UDT_KB_CM_DI";
      T01_Ac_LSL : "UDT_KB_CM_DI";
      T01_Ac_Pump : "UDT_KB_CM_MTR";
      T01_Ac_TT : "UDT_KB_CM_AI";
      T01_Ac_YP_Ac : "UDT_KB_CM_DQ";
      T01_Ac_YP_St : "UDT_KB_CM_DQ";
      T01_Ac_YP_W : "UDT_KB_CM_DQ";
      T01_CaB_CT : "UDT_KB_CM_AI";
      T01_CaB_Heat : "UDT_KB_CM_DQ";
      T01_CaB_LSH : "UDT_KB_CM_DI";
      T01_CaB_LSL : "UDT_KB_CM_DI";
      T01_CaB_Pump : "UDT_KB_CM_MTR";
      T01_CaB_TT : "UDT_KB_CM_AI";
      T01_CaB_YP_Ca : "UDT_KB_CM_DQ";
      T01_CaB_YP_St : "UDT_KB_CM_DQ";
      T01_CaB_YP_W : "UDT_KB_CM_DQ";
      T01_HW_LSH : "UDT_KB_CM_DI";
      T01_HW_LSL : "UDT_KB_CM_DI";
      T01_HW_Pump : "UDT_KB_CM_MTR";
      T01_HW_TT : "UDT_KB_CM_AI";
      T01_HW_YP_St : "UDT_KB_CM_DQ";
      T01_HW_YP_W : "UDT_KB_CM_DQ";
      T01_MW_LSH : "UDT_KB_CM_DI";
      T01_MW_LSL : "UDT_KB_CM_DI";
      T01_MW_Pump : "UDT_KB_CM_MTR";
      T01_MW_YP_W : "UDT_KB_CM_DQ";
   END_STRUCT;


BEGIN

END_DATA_BLOCK

DATA_BLOCK "DBTEST2"
{ S7_Optimized_Access := 'FALSE' }
VERSION : 0.1
NON_RETAIN
   STRUCT 
      "struct" : Struct
         _start : Word;
         _db_id : Word;
         _epoch_ns : ULInt;
         ArData : Array[0..52] of Byte;
         "Counter" : Real;
         _end : Word;
      END_STRUCT;
   END_STRUCT;


BEGIN
   "struct"._start := 16#0B36;
   "struct"._db_id := 201;
   "struct".ArData[0] := 16#00;
   "struct".ArData[1] := 16#D1;
   "struct".ArData[2] := 16#D2;
   "struct".ArData[3] := 16#D3;
   "struct".ArData[4] := 16#D4;
   "struct".ArData[5] := 16#D5;
   "struct".ArData[6] := 16#D6;
   "struct".ArData[7] := 16#D7;
   "struct".ArData[8] := 16#D8;
   "struct".ArData[9] := 16#D9;
   "struct".ArData[10] := 16#DA;
   "struct".ArData[11] := 16#DB;
   "struct".ArData[12] := 16#DC;
   "struct".ArData[13] := 16#DD;
   "struct".ArData[14] := 16#DE;
   "struct".ArData[15] := 16#DF;
   "struct".ArData[16] := 16#E0;
   "struct".ArData[17] := 16#E1;
   "struct".ArData[18] := 16#E2;
   "struct".ArData[19] := 16#E3;
   "struct".ArData[20] := 16#E4;
   "struct".ArData[21] := 16#E5;
   "struct".ArData[22] := 16#E6;
   "struct".ArData[23] := 16#E7;
   "struct".ArData[24] := 16#E8;
   "struct".ArData[25] := 16#E9;
   "struct".ArData[26] := 16#EA;
   "struct".ArData[27] := 16#EB;
   "struct".ArData[28] := 16#EC;
   "struct".ArData[29] := 16#ED;
   "struct".ArData[30] := 16#EE;
   "struct".ArData[31] := 16#EF;
   "struct".ArData[32] := 16#F0;
   "struct".ArData[33] := 16#F1;
   "struct".ArData[34] := 16#F2;
   "struct".ArData[35] := 16#F3;
   "struct".ArData[36] := 16#F4;
   "struct".ArData[37] := 16#F5;
   "struct".ArData[38] := 16#F6;
   "struct".ArData[39] := 16#F7;
   "struct".ArData[40] := 16#F8;
   "struct".ArData[41] := 16#F9;
   "struct".ArData[42] := 16#FA;
   "struct".ArData[43] := 16#FB;
   "struct".ArData[44] := 16#FC;
   "struct".ArData[45] := 16#FD;
   "struct".ArData[46] := 16#FE;
   "struct".ArData[47] := 16#FF;
   "struct".ArData[48] := 16#00;
   "struct".ArData[49] := 16#01;
   "struct".ArData[50] := 16#02;
   "struct".ArData[51] := 16#03;
   "struct".ArData[52] := 16#04;
   "struct"."Counter" := 0.0;
   "struct"._end := 16#1B36;

END_DATA_BLOCK

DATA_BLOCK "DBTEST1"
{ S7_Optimized_Access := 'FALSE' }
VERSION : 0.1
NON_RETAIN
   STRUCT 
      "struct" : Struct
         _start : Word;
         _db_id : Word;
         _epoch_ns : ULInt;
         "Counter" : Word;
         ArData : Array[0..100] of Byte;
         _end { S7_SetPoint := 'True'} : Word;
      END_STRUCT;
   END_STRUCT;


BEGIN
   "struct"._start := 16#0B36;
   "struct"._db_id := 200;
   "struct"."Counter" := 16#0;
   "struct".ArData[0] := 16#00;
   "struct".ArData[1] := 16#E9;
   "struct".ArData[2] := 16#EA;
   "struct".ArData[3] := 16#EB;
   "struct".ArData[4] := 16#EC;
   "struct".ArData[5] := 16#ED;
   "struct".ArData[6] := 16#EE;
   "struct".ArData[7] := 16#EF;
   "struct".ArData[8] := 16#F0;
   "struct".ArData[9] := 16#F1;
   "struct".ArData[10] := 16#F2;
   "struct".ArData[11] := 16#F3;
   "struct".ArData[12] := 16#F4;
   "struct".ArData[13] := 16#F5;
   "struct".ArData[14] := 16#F6;
   "struct".ArData[15] := 16#F7;
   "struct".ArData[16] := 16#F8;
   "struct".ArData[17] := 16#F9;
   "struct".ArData[18] := 16#FA;
   "struct".ArData[19] := 16#FB;
   "struct".ArData[20] := 16#FC;
   "struct".ArData[21] := 16#FD;
   "struct".ArData[22] := 16#FE;
   "struct".ArData[23] := 16#FF;
   "struct".ArData[24] := 16#00;
   "struct".ArData[25] := 16#01;
   "struct".ArData[26] := 16#02;
   "struct".ArData[27] := 16#03;
   "struct".ArData[28] := 16#04;
   "struct".ArData[29] := 16#05;
   "struct".ArData[30] := 16#06;
   "struct".ArData[31] := 16#07;
   "struct".ArData[32] := 16#08;
   "struct".ArData[33] := 16#09;
   "struct".ArData[34] := 16#0A;
   "struct".ArData[35] := 16#0B;
   "struct".ArData[36] := 16#0C;
   "struct".ArData[37] := 16#0D;
   "struct".ArData[38] := 16#0E;
   "struct".ArData[39] := 16#0F;
   "struct".ArData[40] := 16#10;
   "struct".ArData[41] := 16#11;
   "struct".ArData[42] := 16#12;
   "struct".ArData[43] := 16#13;
   "struct".ArData[44] := 16#14;
   "struct".ArData[45] := 16#15;
   "struct".ArData[46] := 16#16;
   "struct".ArData[47] := 16#17;
   "struct".ArData[48] := 16#18;
   "struct".ArData[49] := 16#19;
   "struct".ArData[50] := 16#1A;
   "struct".ArData[51] := 16#1B;
   "struct".ArData[52] := 16#1C;
   "struct".ArData[53] := 16#1D;
   "struct".ArData[54] := 16#1E;
   "struct".ArData[55] := 16#1F;
   "struct".ArData[56] := 16#20;
   "struct".ArData[57] := 16#21;
   "struct".ArData[58] := 16#22;
   "struct".ArData[59] := 16#23;
   "struct".ArData[60] := 16#24;
   "struct".ArData[61] := 16#25;
   "struct".ArData[62] := 16#26;
   "struct".ArData[63] := 16#27;
   "struct".ArData[64] := 16#28;
   "struct".ArData[65] := 16#29;
   "struct".ArData[66] := 16#2A;
   "struct".ArData[67] := 16#2B;
   "struct".ArData[68] := 16#2C;
   "struct".ArData[69] := 16#2D;
   "struct".ArData[70] := 16#2E;
   "struct".ArData[71] := 16#2F;
   "struct".ArData[72] := 16#30;
   "struct".ArData[73] := 16#31;
   "struct".ArData[74] := 16#32;
   "struct".ArData[75] := 16#33;
   "struct".ArData[76] := 16#34;
   "struct".ArData[77] := 16#35;
   "struct".ArData[78] := 16#36;
   "struct".ArData[79] := 16#37;
   "struct".ArData[80] := 16#38;
   "struct".ArData[81] := 16#39;
   "struct".ArData[82] := 16#3A;
   "struct".ArData[83] := 16#3B;
   "struct".ArData[84] := 16#3C;
   "struct".ArData[85] := 16#3D;
   "struct".ArData[86] := 16#3E;
   "struct".ArData[87] := 16#3F;
   "struct".ArData[88] := 16#40;
   "struct".ArData[89] := 16#41;
   "struct".ArData[90] := 16#42;
   "struct".ArData[91] := 16#43;
   "struct".ArData[92] := 16#44;
   "struct".ArData[93] := 16#45;
   "struct".ArData[94] := 16#46;
   "struct".ArData[95] := 16#47;
   "struct".ArData[96] := 16#48;
   "struct".ArData[97] := 16#49;
   "struct".ArData[98] := 16#4A;
   "struct".ArData[99] := 16#4B;
   "struct".ArData[100] := 16#4C;
   "struct"._end := 16#1B36;

END_DATA_BLOCK


