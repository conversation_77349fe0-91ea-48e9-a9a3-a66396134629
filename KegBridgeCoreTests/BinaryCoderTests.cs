using KegBridgeCore.Services.BinaryCoder;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using KegBridgeCore.Utilities;

namespace KegBridgeCoreTests
{
    public class BinaryCoderTests
    {
        [SetUp]
        public void Setup()
        {
        }

        [Test]
        public void Simple_s7_struct()
        {
            List<BinaryStruct> schemas = new List<BinaryStruct>
            {
                new BinaryStruct
                {
                    Name = "udt_kb_ce",
                    Properties = new List<BinaryProperty>
                    {
                        new BinaryProperty
                        {
                            Name = "Counter",
                            Type = new BinaryType { Name = "s7.DInt" }
                        },
                        new BinaryProperty
                        {
                            Name = "Seq",
                            Type = new BinaryStruct
                            {
                                Properties = new List<BinaryProperty>
                                {
                                    new BinaryProperty
                                    {
                                        Name = "Msg_ID",
                                        Type = new BinaryType { Name = "s7.Int" }
                                    },
                                    new BinaryProperty
                                    {
                                        Name = "Msg_Step",
                                        Type = new BinaryType { Name = "s7.Int" }
                                    },
                                    new BinaryProperty
                                    {
                                        Name = "Msg_Stat",
                                        Type = new BinaryType { Name = "s7.Int" }
                                    },
                                    new BinaryProperty
                                    {
                                        Name = "KegID",
                                        Type = new BinaryType { Name = "s7.Int" }
                                    },
                                    new BinaryProperty
                                    {
                                        Name = "ActiveStep",
                                        Type = new BinaryType { Name = "s7.Int" }
                                    },
                                    new BinaryProperty
                                    {
                                        Name = "Val",
                                        Type = new BinaryType { Name = "s7.Real" }
                                    }
                                }
                            }
                        },
                        new BinaryProperty
                        {
                            Name = "CE_Status",
                            Type = new BinaryStruct
                            {
                                Properties = new List<BinaryProperty>
                                {
                                    new BinaryProperty
                                    {
                                        Name = "Action",
                                        Type = new BinaryType { Name = "s7.Int" }
                                    },
                                    new BinaryProperty
                                    {
                                        Name = "Status",
                                        Type = new BinaryType { Name = "s7.Int" }
                                    }
                                }
                            }
                        }
                    }
                },
            };
            
            Schemas.Initialize(schemas);
            Assert.True(Schemas.DecodeCatalog.ContainsKey("udt_kb_ce"));
            Assert.True(Schemas.DecodeCatalog.Count == 1);
            
            Assert.True(ByteArrayConverter.TryConvertString("hex:00000001-0002-0003-0004-0005-0006-00000000-0007-0008", out var buffer));
            
            var result = Schemas.Decode("udt_kb_ce", buffer); 
            
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.TypeOf<ExpandoObject>());
            
            Assert.That(((dynamic)result).Counter, Is.EqualTo(1));
            Assert.That(((dynamic)result).Seq.Msg_ID, Is.EqualTo(2));
            Assert.That(((dynamic)result).Seq.Msg_Step, Is.EqualTo(3));
            Assert.That(((dynamic)result).Seq.Msg_Stat, Is.EqualTo(4));
            Assert.That(((dynamic)result).Seq.KegID, Is.EqualTo(5));
            Assert.That(((dynamic)result).Seq.ActiveStep, Is.EqualTo(6));
            Assert.That(((dynamic)result).Seq.Val, Is.EqualTo(0.0f));
            Assert.That(((dynamic)result).CE_Status.Action, Is.EqualTo(7));
            Assert.That(((dynamic)result).CE_Status.Status, Is.EqualTo(8));
        }

        [Test]
        public void Simple_s7_array()
        {
            
        }
    }
}

// List<BinaryStruct> schemas = new List<BinaryStruct>
// {
//     new BinaryStruct
//     {
//         Name = "DBTEST2",
//         Properties = new List<BinaryProperty>
//         {
//             new BinaryProperty
//             {
//                 Name = "struct",
//                 Type = new BinaryStruct
//                 {
//                     Name = "struct",
//                     Properties = new List<BinaryProperty>
//                     {
//                         new BinaryProperty
//                         {
//                             Name = "_start",
//                             Type = new BinarySimpleType { Name = "s7.Word" }
//                         },
//                         new BinaryProperty
//                         {
//                             Name = "_db_id",
//                             Type = new BinarySimpleType { Name = "s7.Word" }
//                         },
//                         new BinaryProperty
//                         {
//                             Name = "_epoch_ns",
//                             Type = new BinarySimpleType { Name = "s7.ULInt" }
//                         },
//                         new BinaryProperty
//                         {
//                             Name = "ArData",
//                             Type = new BinaryArray
//                             {
//                                 Type = new BinarySimpleType { Name = "s7.Byte" },
//                                 Size = 52
//                             }
//                         },
//                         new BinaryProperty
//                         {
//                             Name = "Counter",
//                             Type = new BinarySimpleType { Name = "s7.Real" }
//                         },
//                         new BinaryProperty
//                         {
//                             Name = "_end",
//                             Type = new BinarySimpleType { Name = "s7.Word" }
//                         }
//                     }
//                 }
//             }
//         }
//     },
//     new BinaryStruct
//     {
//         Name = "udt_kb_ce",
//         Properties = new List<BinaryProperty>
//         {
//             new BinaryProperty
//             {
//                 Name = "Counter",
//                 Type = new BinarySimpleType { Name = "s7.DInt" }
//             },
//             new BinaryProperty
//             {
//                 Name = "Seq",
//                 Type = new BinaryStruct
//                 {
//                     Properties = new List<BinaryProperty>
//                     {
//                         new BinaryProperty
//                         {
//                             Name = "Msg_ID",
//                             Type = new BinarySimpleType { Name = "s7.Int" }
//                         },
//                         new BinaryProperty
//                         {
//                             Name = "Msg_Step",
//                             Type = new BinarySimpleType { Name = "s7.Int" }
//                         },
//                         new BinaryProperty
//                         {
//                             Name = "Msg_Stat",
//                             Type = new BinarySimpleType { Name = "s7.Int" }
//                         },
//                         new BinaryProperty
//                         {
//                             Name = "KegID",
//                             Type = new BinarySimpleType { Name = "s7.Int" }
//                         },
//                         new BinaryProperty
//                         {
//                             Name = "ActiveStep",
//                             Type = new BinarySimpleType { Name = "s7.Int" }
//                         },
//                         new BinaryProperty
//                         {
//                             Name = "Val",
//                             Type = new BinarySimpleType { Name = "s7.Real" }
//                         }
//                     }
//                 }
//             },
//             new BinaryProperty
//             {
//                 Name = "CE_Status",
//                 Type = new BinaryStruct
//                 {
//                     Properties = new List<BinaryProperty>
//                     {
//                         new BinaryProperty
//                         {
//                             Name = "Action",
//                             Type = new BinarySimpleType { Name = "s7.Int" }
//                         },
//                         new BinaryProperty
//                         {
//                             Name = "Status",
//                             Type = new BinarySimpleType { Name = "s7.Int" }
//                         }
//                     }
//                 }
//             }
//         }
//     },
//     new BinaryStruct
//     {
//         Name = "KegBridgeCE",
//         Properties = new List<BinaryProperty>
//         {
//             new BinaryProperty
//             {
//                 Name = "CE_T01_Ac",
//                 Type = new BinaryComplexType { Name = "udt_kb_ce" }
//             },
//             new BinaryProperty
//             {
//                 Name = "CE_T01_Ca",
//                 Type = new BinaryComplexType { Name = "udt_kb_ce" }
//             },
//             new BinaryProperty
//             {
//                 Name = "CE_T01_HW",
//                 Type = new BinaryComplexType { Name = "udt_kb_ce" }
//             },
//             new BinaryProperty
//             {
//                 Name = "CE_T01_MW",
//                 Type = new BinaryComplexType { Name = "udt_kb_ce" }
//             },
//             new BinaryProperty
//             {
//                 Name = "CE_FH",
//                 Type = new BinaryComplexType { Name = "udt_kb_ce" }
//             },
//             new BinaryProperty
//             {
//                 Name = "CE_WH",
//                 Type = new BinaryComplexType { Name = "udt_kb_ce" }
//             }
//         }
//     }
// };
