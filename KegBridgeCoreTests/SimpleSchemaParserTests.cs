using KegBridgeCore.Services.BinaryCoder;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;

namespace KegBridgeCoreTests
{
    public class SimpleSchemaParserTests
    {
        [SetUp]
        public void Setup()
        {
        }

        [Test]
        public void Parse_SimpleStruct_ShouldReturnCorrectAST()
        {
            var schema = @"
                struct TestStruct {
                    counter: s7.word;
                    value: s7.real;
                }
            ";

            var result = SimpleSchemaParser.Parse(schema);

            Assert.That(result, Is.Not.Null);
            Console.WriteLine($"Result count: {result.Count}");
            if (result.Count == 0)
            {
                Console.WriteLine("Parser returned empty result - debugging needed");
            }
            Assert.That(result.Count, Is.EqualTo(1));
            
            var testStruct = result[0];
            Assert.That(testStruct.Name, Is.EqualTo("TestStruct"));
            Assert.That(testStruct.Properties.Count, Is.EqualTo(2));
            
            var counterProp = testStruct.Properties[0];
            Assert.That(counterProp.Name, Is.EqualTo("counter"));
            Assert.That(counterProp.Type, Is.TypeOf<BinaryType>());
            Assert.That(((BinaryType)counterProp.Type).Name, Is.EqualTo("s7.word"));
            
            var valueProp = testStruct.Properties[1];
            Assert.That(valueProp.Name, Is.EqualTo("value"));
            Assert.That(valueProp.Type, Is.TypeOf<BinaryType>());
            Assert.That(((BinaryType)valueProp.Type).Name, Is.EqualTo("s7.real"));
        }

        [Test]
        public void Parse_StructWithArray_ShouldReturnCorrectAST()
        {
            var schema = @"
                struct ArrayStruct {
                    data: array[10] of s7.byte;
                    counter: s7.word;
                }
            ";

            var result = SimpleSchemaParser.Parse(schema);

            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            
            var arrayStruct = result[0];
            Assert.That(arrayStruct.Name, Is.EqualTo("ArrayStruct"));
            Assert.That(arrayStruct.Properties.Count, Is.EqualTo(2));
            
            var dataProp = arrayStruct.Properties[0];
            Assert.That(dataProp.Name, Is.EqualTo("data"));
            Assert.That(dataProp.Type, Is.TypeOf<BinaryArray>());
            
            var arrayType = (BinaryArray)dataProp.Type;
            Assert.That(arrayType.Size, Is.EqualTo(10));
            Assert.That(arrayType.Type, Is.TypeOf<BinaryType>());
            Assert.That(((BinaryType)arrayType.Type).Name, Is.EqualTo("s7.byte"));
        }

        [Test]
        public void Parse_NestedStruct_ShouldReturnCorrectAST()
        {
            var schema = @"
                struct OuterStruct {
                    counter: s7.word;
                    nested: struct {
                        value1: s7.int;
                        value2: s7.real;
                    };
                }
            ";

            var result = SimpleSchemaParser.Parse(schema);

            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            
            var outerStruct = result[0];
            Assert.That(outerStruct.Name, Is.EqualTo("OuterStruct"));
            Assert.That(outerStruct.Properties.Count, Is.EqualTo(2));
            
            var nestedProp = outerStruct.Properties[1];
            Assert.That(nestedProp.Name, Is.EqualTo("nested"));
            Assert.That(nestedProp.Type, Is.TypeOf<BinaryStruct>());
            
            var nestedStruct = (BinaryStruct)nestedProp.Type;
            Assert.That(nestedStruct.Properties.Count, Is.EqualTo(2));
            Assert.That(nestedStruct.Properties[0].Name, Is.EqualTo("value1"));
            Assert.That(nestedStruct.Properties[1].Name, Is.EqualTo("value2"));
        }

        [Test]
        public void Parse_MultipleStructs_ShouldReturnCorrectAST()
        {
            var schema = @"
                struct FirstStruct {
                    value: s7.word;
                }
                
                struct SecondStruct {
                    data: array[5] of s7.byte;
                    counter: s7.int;
                }
            ";

            var result = SimpleSchemaParser.Parse(schema);

            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            
            Assert.That(result[0].Name, Is.EqualTo("FirstStruct"));
            Assert.That(result[1].Name, Is.EqualTo("SecondStruct"));
        }

        [Test]
        public void Parse_ComplexExample_ShouldReturnCorrectAST()
        {
            var schema = @"
                struct ComplexStruct {
                    header: struct {
                        start: s7.word;
                        id: s7.word;
                        timestamp: s7.ulong;
                    };
                    data: array[100] of s7.byte;
                    status: struct {
                        action: s7.int;
                        state: s7.int;
                    };
                    counter: s7.word;
                }
            ";

            var result = SimpleSchemaParser.Parse(schema);

            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            
            var complexStruct = result[0];
            Assert.That(complexStruct.Name, Is.EqualTo("ComplexStruct"));
            Assert.That(complexStruct.Properties.Count, Is.EqualTo(4));
            
            // Verify header struct
            var headerProp = complexStruct.Properties[0];
            Assert.That(headerProp.Name, Is.EqualTo("header"));
            Assert.That(headerProp.Type, Is.TypeOf<BinaryStruct>());
            var headerStruct = (BinaryStruct)headerProp.Type;
            Assert.That(headerStruct.Properties.Count, Is.EqualTo(3));
            
            // Verify array
            var dataProp = complexStruct.Properties[1];
            Assert.That(dataProp.Name, Is.EqualTo("data"));
            Assert.That(dataProp.Type, Is.TypeOf<BinaryArray>());
            var arrayType = (BinaryArray)dataProp.Type;
            Assert.That(arrayType.Size, Is.EqualTo(100));
            
            // Verify status struct
            var statusProp = complexStruct.Properties[2];
            Assert.That(statusProp.Name, Is.EqualTo("status"));
            Assert.That(statusProp.Type, Is.TypeOf<BinaryStruct>());
            var statusStruct = (BinaryStruct)statusProp.Type;
            Assert.That(statusStruct.Properties.Count, Is.EqualTo(2));
        }

        [Test]
        public void Parse_EmptyString_ShouldReturnEmptyList()
        {
            var result = SimpleSchemaParser.Parse("");
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public void Parse_WhitespaceOnly_ShouldReturnEmptyList()
        {
            var result = SimpleSchemaParser.Parse("   \n\t  ");
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
        }

        [Test]
        public void Debug_SimpleTest()
        {
            // Test very simple input
            var result = SimpleSchemaParser.Parse("struct Test { value: int; }");
            Console.WriteLine($"Simple test result count: {result.Count}");

            // Test if the parser itself works
            try
            {
                var parseResult = SimpleSchemaParser.Parser.Parse("struct Test { value: int; }");
                Console.WriteLine($"Direct parser result: {parseResult}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Parser exception: {ex.Message}");
            }
        }

        [Test]
        public void Parse_ManyTopLevelStructs_ShouldReturnCorrectAST()
        {
            var schema = @"
                struct Header {
                    start: s7.word;
                    id: s7.word;
                    timestamp: s7.ulong;
                }

                struct DataPacket {
                    header: Header;
                    data: array[100] of s7.byte;
                    counter: s7.word;
                }

                struct StatusInfo {
                    action: s7.int;
                    state: s7.int;
                    error_code: s7.word;
                    message: array[50] of s7.char;
                }

                struct Configuration {
                    timeout: s7.int;
                    retry_count: s7.int;
                    enabled: s7.bool;
                }
            ";

            var result = SimpleSchemaParser.Parse(schema);

            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(4));

            // Verify all struct names
            Assert.That(result[0].Name, Is.EqualTo("Header"));
            Assert.That(result[1].Name, Is.EqualTo("DataPacket"));
            Assert.That(result[2].Name, Is.EqualTo("StatusInfo"));
            Assert.That(result[3].Name, Is.EqualTo("Configuration"));

            // Verify Header struct
            var headerStruct = result[0];
            Assert.That(headerStruct.Properties.Count, Is.EqualTo(3));
            Assert.That(headerStruct.Properties[0].Name, Is.EqualTo("start"));
            Assert.That(headerStruct.Properties[1].Name, Is.EqualTo("id"));
            Assert.That(headerStruct.Properties[2].Name, Is.EqualTo("timestamp"));

            // Verify DataPacket struct
            var dataPacketStruct = result[1];
            Assert.That(dataPacketStruct.Properties.Count, Is.EqualTo(3));
            Assert.That(dataPacketStruct.Properties[0].Name, Is.EqualTo("header"));
            Assert.That(dataPacketStruct.Properties[1].Name, Is.EqualTo("data"));
            Assert.That(dataPacketStruct.Properties[2].Name, Is.EqualTo("counter"));

            // Verify array in DataPacket
            var dataProperty = dataPacketStruct.Properties[1];
            Assert.That(dataProperty.Type, Is.TypeOf<BinaryArray>());
            var arrayType = (BinaryArray)dataProperty.Type;
            Assert.That(arrayType.Size, Is.EqualTo(100));
        }
    }
}
