#nullable enable
using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Dynamic;
using System.Linq;
using KegBridgeCore.Data;
using NUnit.Framework;

namespace KegBridgeCoreTests;

[TestFixture]
public class NodeEventTests
{
    private const string TestNodeName = "TestNode";
    private const string TestTopic = "TestTopic";

    [Test]
    public void NodeEvent_Constructor_WithValidParameters_ShouldCreateInstance()
    {
        // Arrange
        var data = NodeEventData.Empty;
        var timestamp = DateTimeOffset.UtcNow;

        // Act
        var nodeEvent = new NodeEvent(TestNodeName, data, TestTopic, timestamp);

        // Assert
        Assert.That(nodeEvent.Node, Is.EqualTo(TestNodeName));
        Assert.That(nodeEvent.Topic, Is.EqualTo(TestTopic));
        Assert.That(nodeEvent.Timestamp, Is.EqualTo(timestamp));
        Assert.That(nodeEvent.CreatedAt, Is.Not.EqualTo(timestamp));
        Assert.That(nodeEvent.Data, Is.EqualTo(data));
        Assert.That(nodeEvent.Id, Is.Not.EqualTo(Guid.Empty));
    }

    [Test]
    public void NodeEvent_Constructor_WithNullOrEmptyNode_ShouldThrowArgumentException()
    {
        // Arrange
        var data = NodeEventData.Empty;

        // Act & Assert
        Assert.Throws<ArgumentException>(() => new NodeEvent(null!, data));
        Assert.Throws<ArgumentException>(() => new NodeEvent("", data));
        Assert.Throws<ArgumentException>(() => new NodeEvent("   ", data));
    }

    [Test]
    public void NodeEvent_Constructor_WithNullData_ShouldUseEmptyData()
    {
        // Act
        var nodeEvent = new NodeEvent(TestNodeName, null!);

        // Assert
        Assert.That(nodeEvent.Data, Is.EqualTo(NodeEventData.Empty));
    }

    [Test]
    public void NodeEvent_Constructor_WithoutTimestamp_ShouldUseCurrentTime()
    {
        // Arrange
        var before = DateTimeOffset.UtcNow;

        // Act
        var nodeEvent = new NodeEvent(TestNodeName, NodeEventData.Empty);
        var after = DateTimeOffset.UtcNow;

        // Assert
        Assert.That(nodeEvent.Timestamp, Is.GreaterThanOrEqualTo(before));
        Assert.That(nodeEvent.Timestamp, Is.LessThanOrEqualTo(after));
        Assert.That(nodeEvent.CreatedAt, Is.EqualTo(nodeEvent.Timestamp));
    }

    [Test]
    public void NodeEvent_FromExpando_ShouldCreateNodeEventWithCorrectData()
    {
        // Arrange
        dynamic expando = new ExpandoObject();
        expando.key1 = "value1";
        expando.key2 = 42;
        var timestamp = DateTimeOffset.UtcNow;

        // Act
        var nodeEvent = NodeEvent.FromExpando(TestNodeName, expando, TestTopic, timestamp);

        // Assert
        Assert.That(nodeEvent.Node, Is.EqualTo(TestNodeName));
        Assert.That(nodeEvent.Topic, Is.EqualTo(TestTopic));
        Assert.That(nodeEvent.Timestamp, Is.EqualTo(timestamp));
        Assert.That(nodeEvent.Data["key1"], Is.EqualTo("value1"));
        Assert.That(nodeEvent.Data["key2"], Is.EqualTo(42));
    }

    [Test]
    public void NodeEvent_FromDictionary_ShouldCreateNodeEventWithCorrectData()
    {
        // Arrange
        var dict = new Dictionary<string, object?>
        {
            ["key1"] = "value1",
            ["key2"] = 42,
            ["key3"] = null
        };
        var timestamp = DateTimeOffset.UtcNow;

        // Act
        var nodeEvent = NodeEvent.FromDictionary(TestNodeName, dict, TestTopic, timestamp);

        // Assert
        Assert.That(nodeEvent.Node, Is.EqualTo(TestNodeName));
        Assert.That(nodeEvent.Topic, Is.EqualTo(TestTopic));
        Assert.That(nodeEvent.Timestamp, Is.EqualTo(timestamp));
        Assert.That(nodeEvent.Data["key1"], Is.EqualTo("value1"));
        Assert.That(nodeEvent.Data["key2"], Is.EqualTo(42));
        Assert.That(nodeEvent.Data["key3"], Is.Null);
    }

    [Test]
    public void NodeEvent_With_ShouldReturnNewInstanceWithAddedData()
    {
        // Arrange
        var original = new NodeEvent(TestNodeName, NodeEventData.Empty);

        // Act
        var modified = original.With("newKey", "newValue");

        // Assert
        Assert.That(original.Data.ContainsKey("newKey"), Is.False);
        Assert.That(modified.Data["newKey"], Is.EqualTo("newValue"));
        Assert.That(modified.Node, Is.EqualTo(original.Node));
        Assert.That(modified.Topic, Is.EqualTo(original.Topic));
        Assert.That(modified.Timestamp, Is.EqualTo(original.Timestamp));
        Assert.That(modified.Id, Is.Not.EqualTo(original.Id)); // New instance has new ID
    }

    [Test]
    public void NodeEvent_WithRange_ShouldReturnNewInstanceWithAddedData()
    {
        // Arrange
        var original = new NodeEvent(TestNodeName, NodeEventData.Empty);
        var items = new[]
        {
            new KeyValuePair<string, object?>("key1", "value1"),
            new KeyValuePair<string, object?>("key2", 42)
        };

        // Act
        var modified = original.WithRange(items);

        // Assert
        Assert.That(original.Data.Count, Is.EqualTo(0));
        Assert.That(modified.Data.Count, Is.EqualTo(2));
        Assert.That(modified.Data["key1"], Is.EqualTo("value1"));
        Assert.That(modified.Data["key2"], Is.EqualTo(42));
    }

    [Test]
    public void NodeEvent_Without_ShouldReturnNewInstanceWithRemovedData()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["key1"] = "value1",
            ["key2"] = "value2"
        });
        var original = new NodeEvent(TestNodeName, data);

        // Act
        var modified = original.Without("key1");

        // Assert
        Assert.That(original.Data.ContainsKey("key1"), Is.True);
        Assert.That(modified.Data.ContainsKey("key1"), Is.False);
        Assert.That(modified.Data["key2"], Is.EqualTo("value2"));
    }

    [Test]
    public void NodeEvent_WithoutRange_ShouldReturnNewInstanceWithRemovedData()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["key1"] = "value1",
            ["key2"] = "value2",
            ["key3"] = "value3"
        });
        var original = new NodeEvent(TestNodeName, data);

        // Act
        var modified = original.WithoutRange(new[] { "key1", "key3" });

        // Assert
        Assert.That(original.Data.Count, Is.EqualTo(3));
        Assert.That(modified.Data.Count, Is.EqualTo(1));
        Assert.That(modified.Data.ContainsKey("key2"), Is.True);
        Assert.That(modified.Data["key2"], Is.EqualTo("value2"));
    }

    [Test]
    public void NodeEvent_WithTopic_ShouldReturnNewInstanceWithUpdatedTopic()
    {
        // Arrange
        var original = new NodeEvent(TestNodeName, NodeEventData.Empty, "originalTopic");

        // Act
        var modified = original.WithTopic("newTopic");

        // Assert
        Assert.That(original.Topic, Is.EqualTo("originalTopic"));
        Assert.That(modified.Topic, Is.EqualTo("newTopic"));
        Assert.That(modified.Node, Is.EqualTo(original.Node));
        Assert.That(modified.Data, Is.EqualTo(original.Data));
        Assert.That(modified.Timestamp, Is.EqualTo(original.Timestamp));
    }

    [Test]
    public void NodeEvent_WithNode_ShouldReturnNewInstanceWithUpdatedNode()
    {
        // Arrange
        var original = new NodeEvent(TestNodeName, NodeEventData.Empty);

        // Act
        var modified = original.WithNode("NewNodeName");

        // Assert
        Assert.That(original.Node, Is.EqualTo(TestNodeName));
        Assert.That(modified.Node, Is.EqualTo("NewNodeName"));
        Assert.That(modified.Topic, Is.EqualTo(original.Topic));
        Assert.That(modified.Data, Is.EqualTo(original.Data));
        Assert.That(modified.Timestamp, Is.EqualTo(original.Timestamp));
    }

    [Test]
    public void NodeEvent_WithNode_WithInvalidNode_ShouldThrowArgumentException()
    {
        // Arrange
        var original = new NodeEvent(TestNodeName, NodeEventData.Empty);

        // Act & Assert
        Assert.Throws<ArgumentException>(() => original.WithNode(null!));
        Assert.Throws<ArgumentException>(() => original.WithNode(""));
        Assert.Throws<ArgumentException>(() => original.WithNode("   "));
    }

    [Test]
    public void NodeEvent_TryGet_WithExistingKey_ShouldReturnTrueAndValue()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["stringKey"] = "stringValue",
            ["intKey"] = 42,
            ["nullKey"] = null
        });
        var nodeEvent = new NodeEvent(TestNodeName, data);

        // Act & Assert
        Assert.That(nodeEvent.TryGet<string>("stringKey", out var stringValue), Is.True);
        Assert.That(stringValue, Is.EqualTo("stringValue"));

        Assert.That(nodeEvent.TryGet<int>("intKey", out var intValue), Is.True);
        Assert.That(intValue, Is.EqualTo(42));

        Assert.That(nodeEvent.TryGet<string>("nullKey", out var nullValue), Is.False);
        Assert.That(nullValue, Is.Null);
    }

    [Test]
    public void NodeEvent_TryGet_WithNonExistingKey_ShouldReturnFalse()
    {
        // Arrange
        var nodeEvent = new NodeEvent(TestNodeName, NodeEventData.Empty);

        // Act & Assert
        Assert.That(nodeEvent.TryGet<string>("nonExistingKey", out var value), Is.False);
        Assert.That(value, Is.Null);
    }

    [Test]
    public void NodeEvent_TryGet_WithWrongType_ShouldReturnFalse()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?> { ["key"] = "stringValue" });
        var nodeEvent = new NodeEvent(TestNodeName, data);

        // Act & Assert
        Assert.That(nodeEvent.TryGet<int>("key", out var value), Is.False);
        Assert.That(value, Is.EqualTo(0));
    }

    [Test]
    public void NodeEvent_IsImmutable_ModifyingOriginalDataShouldNotAffectNodeEvent()
    {
        // Arrange
        var originalDict = new Dictionary<string, object?> { ["key"] = "originalValue" };
        var nodeEvent = NodeEvent.FromDictionary(TestNodeName, originalDict);

        // Act
        originalDict["key"] = "modifiedValue";
        originalDict["newKey"] = "newValue";

        // Assert
        Assert.That(nodeEvent.Data["key"], Is.EqualTo("originalValue"));
        Assert.That(nodeEvent.Data.ContainsKey("newKey"), Is.False);
    }

    [Test]
    public void NodeEvent_EachInstanceHasUniqueId()
    {
        // Arrange & Act
        var event1 = new NodeEvent(TestNodeName, NodeEventData.Empty);
        var event2 = new NodeEvent(TestNodeName, NodeEventData.Empty);
        var event3 = event1.With("key", "value");

        // Assert
        Assert.That(event1.Id, Is.Not.EqualTo(event2.Id));
        Assert.That(event1.Id, Is.Not.EqualTo(event3.Id));
        Assert.That(event2.Id, Is.Not.EqualTo(event3.Id));
    }

    [Test]
    public void NodeEvent_GetPath_ShouldDelegateToData()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["user"] = new Dictionary<string, object?> { ["name"] = "Eve" }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data);

        // Act & Assert
        Assert.That(nodeEvent.GetPath("user.name"), Is.EqualTo("Eve"));
        Assert.That(nodeEvent.GetPath("nonexistent"), Is.Null);
    }

    [Test]
    public void NodeEvent_TryGetPath_ShouldDelegateToData()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["config"] = new Dictionary<string, object?> { ["timeout"] = 5000 }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data);

        // Act & Assert
        Assert.That(nodeEvent.TryGetPath("config.timeout", out var timeout), Is.True);
        Assert.That(timeout, Is.EqualTo(5000));

        Assert.That(nodeEvent.TryGetPath<int>("config.timeout", out var typedTimeout), Is.True);
        Assert.That(typedTimeout, Is.EqualTo(5000));

        Assert.That(nodeEvent.TryGetPath("config.nonexistent", out var missing), Is.False);
        Assert.That(missing, Is.Null);
    }

    [Test]
    public void NodeEvent_SetPath_ShouldReturnNewInstanceWithUpdatedData()
    {
        // Arrange
        var original = new NodeEvent(TestNodeName, NodeEventData.Empty, TestTopic);

        // Act
        var modified = original.SetPath("settings.debug", true);

        // Assert
        Assert.That(original.GetPath("settings.debug"), Is.Null);
        Assert.That(modified.GetPath("settings.debug"), Is.EqualTo(true));

        // Verify other properties are preserved
        Assert.That(modified.Node, Is.EqualTo(original.Node));
        Assert.That(modified.Topic, Is.EqualTo(original.Topic));
        Assert.That(modified.Timestamp, Is.EqualTo(original.Timestamp));
        Assert.That(modified.Id, Is.Not.EqualTo(original.Id)); // New instance has new ID
    }

    [Test]
    public void NodeEvent_SetPath_WithComplexNestedPath_ShouldWork()
    {
        // Arrange
        var original = new NodeEvent(TestNodeName, NodeEventData.Empty);

        // Act
        var modified = original.SetPath("api.endpoints.users.get.url", "/api/v1/users");

        // Assert
        Assert.That(modified.GetPath("api.endpoints.users.get.url"), Is.EqualTo("/api/v1/users"));
    }

    [Test]
    public void NodeEvent_SetPaths_WithKeyValuePairs_ShouldSetMultipleValues()
    {
        // Arrange
        var original = new NodeEvent(TestNodeName, NodeEventData.Empty, TestTopic);
        var pathValues = new[]
        {
            new KeyValuePair<string, object?>("user.name", "David"),
            new KeyValuePair<string, object?>("user.profile.role", "admin"),
            new KeyValuePair<string, object?>("session.timeout", 3600),
            new KeyValuePair<string, object?>("features.darkMode", true)
        };

        // Act
        var modified = original.SetPaths(pathValues);

        // Assert
        Assert.That(modified.GetPath("user.name"), Is.EqualTo("David"));
        Assert.That(modified.GetPath("user.profile.role"), Is.EqualTo("admin"));
        Assert.That(modified.GetPath("session.timeout"), Is.EqualTo(3600));
        Assert.That(modified.GetPath("features.darkMode"), Is.EqualTo(true));

        // Verify other properties are preserved
        Assert.That(modified.Node, Is.EqualTo(original.Node));
        Assert.That(modified.Topic, Is.EqualTo(original.Topic));
        Assert.That(modified.Timestamp, Is.EqualTo(original.Timestamp));
        Assert.That(modified.Id, Is.Not.EqualTo(original.Id)); // New instance has new ID
    }

    [Test]
    public void NodeEvent_SetPaths_WithAnonymousObject_ShouldSetValues()
    {
        // Arrange
        var original = new NodeEvent(TestNodeName, NodeEventData.Empty);

        // Act
        var modified = original.SetPaths(new
        {
            Version = "2.1.0",
            Environment = "staging",
            Debug = true,
            MaxConnections = 100
        });

        // Assert
        Assert.That(modified.GetPath("Version"), Is.EqualTo("2.1.0"));
        Assert.That(modified.GetPath("Environment"), Is.EqualTo("staging"));
        Assert.That(modified.GetPath("Debug"), Is.EqualTo(true));
        Assert.That(modified.GetPath("MaxConnections"), Is.EqualTo(100));
    }

    [Test]
    public void NodeEvent_SetPaths_WithExistingData_ShouldMergeCorrectly()
    {
        // Arrange
        var existingData = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["app"] = new Dictionary<string, object?> { ["name"] = "MyApp" },
            ["config"] = new Dictionary<string, object?> { ["port"] = 8080 }
        });
        var original = new NodeEvent(TestNodeName, existingData);

        var pathValues = new[]
        {
            new KeyValuePair<string, object?>("app.version", "1.0.0"),
            new KeyValuePair<string, object?>("config.host", "localhost"),
            new KeyValuePair<string, object?>("database.url", "mongodb://localhost:27017")
        };

        // Act
        var modified = original.SetPaths(pathValues);

        // Assert
        // New values
        Assert.That(modified.GetPath("app.version"), Is.EqualTo("1.0.0"));
        Assert.That(modified.GetPath("config.host"), Is.EqualTo("localhost"));
        Assert.That(modified.GetPath("database.url"), Is.EqualTo("mongodb://localhost:27017"));

        // Preserved values
        Assert.That(modified.GetPath("app.name"), Is.EqualTo("MyApp"));
        Assert.That(modified.GetPath("config.port"), Is.EqualTo(8080));
    }

    [Test]
    public void NodeEvent_SetPaths_WithNullArgument_ShouldThrowArgumentNullException()
    {
        // Arrange
        var nodeEvent = new NodeEvent(TestNodeName, NodeEventData.Empty);

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => nodeEvent.SetPaths((IEnumerable<KeyValuePair<string, object?>>)null!));
        Assert.Throws<ArgumentNullException>(() => nodeEvent.SetPaths((object)null!));
    }

    [Test]
    public void NodeEvent_SetPaths_ComplexScenario_ShouldWork()
    {
        // Arrange
        var original = new NodeEvent(TestNodeName, NodeEventData.Empty, "system.config");

        // Act - Set up a complex configuration structure in one go
        var configured = original.SetPaths(new[]
        {
            new KeyValuePair<string, object?>("server.host", "api.example.com"),
            new KeyValuePair<string, object?>("server.port", 443),
            new KeyValuePair<string, object?>("server.ssl.enabled", true),
            new KeyValuePair<string, object?>("server.ssl.certificate", "/path/to/cert.pem"),
            new KeyValuePair<string, object?>("database.primary.host", "db1.example.com"),
            new KeyValuePair<string, object?>("database.primary.port", 5432),
            new KeyValuePair<string, object?>("database.replica.host", "db2.example.com"),
            new KeyValuePair<string, object?>("database.replica.port", 5432),
            new KeyValuePair<string, object?>("cache.redis.host", "redis.example.com"),
            new KeyValuePair<string, object?>("cache.redis.port", 6379),
            new KeyValuePair<string, object?>("logging.level", "INFO"),
            new KeyValuePair<string, object?>("logging.format", "json")
        });

        // Assert
        Assert.That(configured.GetPath("server.host"), Is.EqualTo("api.example.com"));
        Assert.That(configured.GetPath("server.port"), Is.EqualTo(443));
        Assert.That(configured.GetPath("server.ssl.enabled"), Is.EqualTo(true));
        Assert.That(configured.GetPath("server.ssl.certificate"), Is.EqualTo("/path/to/cert.pem"));
        Assert.That(configured.GetPath("database.primary.host"), Is.EqualTo("db1.example.com"));
        Assert.That(configured.GetPath("database.replica.port"), Is.EqualTo(5432));
        Assert.That(configured.GetPath("cache.redis.host"), Is.EqualTo("redis.example.com"));
        Assert.That(configured.GetPath("logging.level"), Is.EqualTo("INFO"));
        Assert.That(configured.GetPath("logging.format"), Is.EqualTo("json"));
    }
}

[TestFixture]
public class NodeEventDataTests
{
    [Test]
    public void NodeEventData_Empty_ShouldHaveZeroCount()
    {
        // Act & Assert
        Assert.That(NodeEventData.Empty.Count, Is.EqualTo(0));
        Assert.That(NodeEventData.Empty.Keys, Is.Empty);
        Assert.That(NodeEventData.Empty.Values, Is.Empty);
    }

    [Test]
    public void NodeEventData_FromExpando_ShouldCreateCorrectData()
    {
        // Arrange
        dynamic expando = new ExpandoObject();
        expando.key1 = "value1";
        expando.key2 = 42;
        expando.key3 = null;

        // Act
        var data = NodeEventData.FromExpando(expando);

        // Assert
        Assert.That(data.Count, Is.EqualTo(3));
        Assert.That(data["key1"], Is.EqualTo("value1"));
        Assert.That(data["key2"], Is.EqualTo(42));
        Assert.That(data["key3"], Is.Null);
    }

    [Test]
    public void NodeEventData_FromDictionary_ShouldCreateCorrectData()
    {
        // Arrange
        var dict = new Dictionary<string, object?>
        {
            ["key1"] = "value1",
            ["key2"] = 42,
            ["key3"] = null
        };

        // Act
        var data = NodeEventData.FromDictionary(dict);

        // Assert
        Assert.That(data.Count, Is.EqualTo(3));
        Assert.That(data["key1"], Is.EqualTo("value1"));
        Assert.That(data["key2"], Is.EqualTo(42));
        Assert.That(data["key3"], Is.Null);
    }

    [Test]
    public void NodeEventData_FromPairs_ShouldCreateCorrectData()
    {
        // Arrange
        var pairs = new[]
        {
            new KeyValuePair<string, object?>("key1", "value1"),
            new KeyValuePair<string, object?>("key2", 42),
            new KeyValuePair<string, object?>("key3", null)
        };

        // Act
        var data = NodeEventData.FromPairs(pairs);

        // Assert
        Assert.That(data.Count, Is.EqualTo(3));
        Assert.That(data["key1"], Is.EqualTo("value1"));
        Assert.That(data["key2"], Is.EqualTo(42));
        Assert.That(data["key3"], Is.Null);
    }

    [Test]
    public void NodeEventData_With_ShouldReturnNewInstanceWithAddedData()
    {
        // Arrange
        var original = NodeEventData.Empty;

        // Act
        var modified = original.With("newKey", "newValue");

        // Assert
        Assert.That(original.Count, Is.EqualTo(0));
        Assert.That(modified.Count, Is.EqualTo(1));
        Assert.That(modified["newKey"], Is.EqualTo("newValue"));
    }

    [Test]
    public void NodeEventData_With_ShouldOverwriteExistingKey()
    {
        // Arrange
        var original = NodeEventData.FromDictionary(new Dictionary<string, object?> { ["key"] = "oldValue" });

        // Act
        var modified = original.With("key", "newValue");

        // Assert
        Assert.That(original["key"], Is.EqualTo("oldValue"));
        Assert.That(modified["key"], Is.EqualTo("newValue"));
    }

    [Test]
    public void NodeEventData_WithRange_ShouldReturnNewInstanceWithAddedData()
    {
        // Arrange
        var original = NodeEventData.FromDictionary(new Dictionary<string, object?> { ["existing"] = "value" });
        var items = new[]
        {
            new KeyValuePair<string, object?>("key1", "value1"),
            new KeyValuePair<string, object?>("key2", 42)
        };

        // Act
        var modified = original.WithRange(items);

        // Assert
        Assert.That(original.Count, Is.EqualTo(1));
        Assert.That(modified.Count, Is.EqualTo(3));
        Assert.That(modified["existing"], Is.EqualTo("value"));
        Assert.That(modified["key1"], Is.EqualTo("value1"));
        Assert.That(modified["key2"], Is.EqualTo(42));
    }

    [Test]
    public void NodeEventData_Without_ShouldReturnNewInstanceWithRemovedData()
    {
        // Arrange
        var original = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["key1"] = "value1",
            ["key2"] = "value2"
        });

        // Act
        var modified = original.Without("key1");

        // Assert
        Assert.That(original.Count, Is.EqualTo(2));
        Assert.That(modified.Count, Is.EqualTo(1));
        Assert.That(modified.ContainsKey("key1"), Is.False);
        Assert.That(modified["key2"], Is.EqualTo("value2"));
    }

    [Test]
    public void NodeEventData_Without_WithNonExistingKey_ShouldReturnSameStructure()
    {
        // Arrange
        var original = NodeEventData.FromDictionary(new Dictionary<string, object?> { ["key"] = "value" });

        // Act
        var modified = original.Without("nonExistingKey");

        // Assert
        Assert.That(modified.Count, Is.EqualTo(1));
        Assert.That(modified["key"], Is.EqualTo("value"));
    }

    [Test]
    public void NodeEventData_WithoutRange_ShouldReturnNewInstanceWithRemovedData()
    {
        // Arrange
        var original = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["key1"] = "value1",
            ["key2"] = "value2",
            ["key3"] = "value3"
        });

        // Act
        var modified = original.WithoutRange(new[] { "key1", "key3" });

        // Assert
        Assert.That(original.Count, Is.EqualTo(3));
        Assert.That(modified.Count, Is.EqualTo(1));
        Assert.That(modified.ContainsKey("key2"), Is.True);
        Assert.That(modified["key2"], Is.EqualTo("value2"));
    }

    [Test]
    public void NodeEventData_TryGet_WithExistingKey_ShouldReturnTrueAndValue()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["stringKey"] = "stringValue",
            ["intKey"] = 42,
            ["nullKey"] = null
        });

        // Act & Assert
        Assert.That(data.TryGet<string>("stringKey", out var stringValue), Is.True);
        Assert.That(stringValue, Is.EqualTo("stringValue"));

        Assert.That(data.TryGet<int>("intKey", out var intValue), Is.True);
        Assert.That(intValue, Is.EqualTo(42));

        Assert.That(data.TryGet<string>("nullKey", out var nullValue), Is.False);
        Assert.That(nullValue, Is.Null);
    }

    [Test]
    public void NodeEventData_TryGet_WithNonExistingKey_ShouldReturnFalse()
    {
        // Arrange
        var data = NodeEventData.Empty;

        // Act & Assert
        Assert.That(data.TryGet<string>("nonExistingKey", out var value), Is.False);
        Assert.That(value, Is.Null);
    }

    [Test]
    public void NodeEventData_TryGet_WithWrongType_ShouldReturnFalse()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?> { ["key"] = "stringValue" });

        // Act & Assert
        Assert.That(data.TryGet<int>("key", out var value), Is.False);
        Assert.That(value, Is.EqualTo(0));
    }

    [Test]
    public void NodeEventData_IReadOnlyDictionary_ShouldImplementCorrectly()
    {
        // Arrange
        var dict = new Dictionary<string, object?>
        {
            ["key1"] = "value1",
            ["key2"] = 42
        };
        var data = NodeEventData.FromDictionary(dict);

        // Act & Assert
        Assert.That(data.Count, Is.EqualTo(2));
        Assert.That(data.Keys, Contains.Item("key1"));
        Assert.That(data.Keys, Contains.Item("key2"));
        Assert.That(data.Values, Contains.Item("value1"));
        Assert.That(data.Values, Contains.Item(42));
        Assert.That(data["key1"], Is.EqualTo("value1"));
        Assert.That(data.ContainsKey("key1"), Is.True);
        Assert.That(data.ContainsKey("nonExisting"), Is.False);
        Assert.That(data.TryGetValue("key1", out var value), Is.True);
        Assert.That(value, Is.EqualTo("value1"));
    }

    [Test]
    public void NodeEventData_Enumeration_ShouldWork()
    {
        // Arrange
        var dict = new Dictionary<string, object?>
        {
            ["key1"] = "value1",
            ["key2"] = 42
        };
        var data = NodeEventData.FromDictionary(dict);

        // Act
        var pairs = data.ToList();

        // Assert
        Assert.That(pairs.Count, Is.EqualTo(2));
        Assert.That(pairs.Any(p => p.Key == "key1" && p.Value!.Equals("value1")), Is.True);
        Assert.That(pairs.Any(p => p.Key == "key2" && p.Value!.Equals(42)), Is.True);
    }

    [Test]
    public void NodeEventData_DeepFreeze_NestedDictionary_ShouldBecomeImmutable()
    {
        // Arrange
        var nestedDict = new Dictionary<string, object?> { ["nested"] = "value" };
        var originalDict = new Dictionary<string, object?> { ["outer"] = nestedDict };

        // Act
        var data = NodeEventData.FromDictionary(originalDict);

        // Modify original nested dictionary
        nestedDict["nested"] = "modifiedValue";
        nestedDict["newKey"] = "newValue";

        // Assert
        var frozenNested = (ImmutableDictionary<string, object?>)data["outer"];
        Assert.That(frozenNested["nested"], Is.EqualTo("value"));
        Assert.That(frozenNested.ContainsKey("newKey"), Is.False);
    }

    [Test]
    public void NodeEventData_DeepFreeze_NestedExpando_ShouldBecomeImmutable()
    {
        // Arrange
        dynamic nestedExpando = new ExpandoObject();
        nestedExpando.nested = "value";

        dynamic outerExpando = new ExpandoObject();
        outerExpando.outer = nestedExpando;

        // Act
        var data = NodeEventData.FromExpando(outerExpando);

        // Modify original nested expando
        nestedExpando.nested = "modifiedValue";
        nestedExpando.newKey = "newValue";

        // Assert
        var frozenNested = (ImmutableDictionary<string, object?>)data["outer"];
        Assert.That(frozenNested["nested"], Is.EqualTo("value"));
        Assert.That(frozenNested.ContainsKey("newKey"), Is.False);
    }

    [Test]
    public void NodeEventData_DeepFreeze_NestedArray_ShouldBecomeImmutable()
    {
        // Arrange
        var nestedArray = new List<object?> { "item1", 42, null };
        var originalDict = new Dictionary<string, object?> { ["array"] = nestedArray };

        // Act
        var data = NodeEventData.FromDictionary(originalDict);

        // Modify original array
        nestedArray.Add("newItem");
        nestedArray[0] = "modifiedItem";

        // Assert
        var frozenArray = (ImmutableArray<object?>)data["array"];
        Assert.That(frozenArray.Length, Is.EqualTo(3));
        Assert.That(frozenArray[0], Is.EqualTo("item1"));
        Assert.That(frozenArray[1], Is.EqualTo(42));
        Assert.That(frozenArray[2], Is.Null);
    }

    [Test]
    public void NodeEventData_DeepFreeze_ComplexNestedStructure_ShouldBecomeFullyImmutable()
    {
        // Arrange
        var deepArray = new List<object?> { "deep", 123 };
        var deepDict = new Dictionary<string, object?> { ["deepArray"] = deepArray, ["deepValue"] = "deep" };
        var midArray = new List<object?> { deepDict, "midValue" };
        var originalDict = new Dictionary<string, object?>
        {
            ["topLevel"] = "topValue",
            ["midLevel"] = midArray,
            ["directArray"] = new[] { 1, 2, 3 }
        };

        // Act
        var data = NodeEventData.FromDictionary(originalDict);

        // Modify original structures
        deepArray.Add("newDeepItem");
        deepDict["newDeepKey"] = "newDeepValue";
        midArray.Add("newMidItem");

        // Assert
        Assert.That(data["topLevel"], Is.EqualTo("topValue"));

        var frozenMidArray = (ImmutableArray<object?>)data["midLevel"];
        Assert.That(frozenMidArray.Length, Is.EqualTo(2));

        var frozenDeepDict = (ImmutableDictionary<string, object?>)frozenMidArray[0];
        Assert.That(frozenDeepDict["deepValue"], Is.EqualTo("deep"));
        Assert.That(frozenDeepDict.ContainsKey("newDeepKey"), Is.False);

        var frozenDeepArray = (ImmutableArray<object?>)frozenDeepDict["deepArray"];
        Assert.That(frozenDeepArray.Length, Is.EqualTo(2));
        Assert.That(frozenDeepArray[0], Is.EqualTo("deep"));
        Assert.That(frozenDeepArray[1], Is.EqualTo(123));
    }

    [Test]
    public void NodeEventData_DeepFreeze_NonGenericDictionary_ShouldBecomeImmutable()
    {
        // Arrange
        var nonGenericDict = new System.Collections.Hashtable
        {
            ["key1"] = "value1",
            ["key2"] = 42
        };
        var originalDict = new Dictionary<string, object?> { ["hashtable"] = nonGenericDict };

        // Act
        var data = NodeEventData.FromDictionary(originalDict);

        // Modify original hashtable
        nonGenericDict["key1"] = "modifiedValue";
        nonGenericDict["newKey"] = "newValue";

        // Assert
        var frozenDict = (ImmutableDictionary<string, object?>)data["hashtable"];
        Assert.That(frozenDict["key1"], Is.EqualTo("value1"));
        Assert.That(frozenDict["key2"], Is.EqualTo(42));
        Assert.That(frozenDict.ContainsKey("newKey"), Is.False);
    }

    [Test]
    public void NodeEventData_DeepFreeze_PrimitiveTypes_ShouldRemainUnchanged()
    {
        // Arrange
        var originalDict = new Dictionary<string, object?>
        {
            ["string"] = "test",
            ["int"] = 42,
            ["double"] = 3.14,
            ["bool"] = true,
            ["null"] = null,
            ["guid"] = Guid.NewGuid(),
            ["datetime"] = DateTime.UtcNow
        };

        // Act
        var data = NodeEventData.FromDictionary(originalDict);

        // Assert - primitive types should be preserved as-is
        Assert.That(data["string"], Is.EqualTo("test"));
        Assert.That(data["int"], Is.EqualTo(42));
        Assert.That(data["double"], Is.EqualTo(3.14));
        Assert.That(data["bool"], Is.EqualTo(true));
        Assert.That(data["null"], Is.Null);
        Assert.That(data["guid"], Is.EqualTo(originalDict["guid"]));
        Assert.That(data["datetime"], Is.EqualTo(originalDict["datetime"]));
    }

    [Test]
    public void NodeEventData_IsImmutable_ModifyingOriginalDataShouldNotAffectNodeEventData()
    {
        // Arrange
        var originalDict = new Dictionary<string, object?> { ["key"] = "originalValue" };
        var data = NodeEventData.FromDictionary(originalDict);

        // Act
        originalDict["key"] = "modifiedValue";
        originalDict["newKey"] = "newValue";

        // Assert
        Assert.That(data["key"], Is.EqualTo("originalValue"));
        Assert.That(data.ContainsKey("newKey"), Is.False);
    }

    [Test]
    public void NodeEventData_RecordEquality_ShouldWorkCorrectly()
    {
        // Arrange
        var dict1 = new Dictionary<string, object?> { ["key"] = "value" };
        var dict2 = new Dictionary<string, object?> { ["key"] = "value" };
        var data1 = NodeEventData.FromDictionary(dict1);
        var data2 = NodeEventData.FromDictionary(dict2);
        var data3 = data1.With("newKey", "newValue");

        // Assert
        // Note: Record equality for NodeEventData depends on ImmutableDictionary equality
        // which compares by reference, not content. So we test functional equality instead.
        Assert.That(data1.Count, Is.EqualTo(data2.Count));
        Assert.That(data1["key"], Is.EqualTo(data2["key"]));
        Assert.That(data1.Count, Is.Not.EqualTo(data3.Count)); // Different content should have different count
        Assert.That(data3.ContainsKey("newKey"), Is.True);
        Assert.That(data1.ContainsKey("newKey"), Is.False);
    }

    [Test]
    public void NodeEventData_IndexerAccess_WithNonExistingKey_ShouldThrowKeyNotFoundException()
    {
        // Arrange
        var data = NodeEventData.Empty;

        // Act & Assert
        Assert.Throws<KeyNotFoundException>(() => { var _ = data["nonExistingKey"]; });
    }

    [Test]
    public void NodeEventData_GetPath_WithSimplePath_ShouldReturnValue()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["name"] = "John",
            ["age"] = 30
        });

        // Act & Assert
        Assert.That(data.GetPath("name"), Is.EqualTo("John"));
        Assert.That(data.GetPath("age"), Is.EqualTo(30));
        Assert.That(data.GetPath("nonexistent"), Is.Null);
    }

    [Test]
    public void NodeEventData_GetPath_WithNestedPath_ShouldReturnValue()
    {
        // Arrange
        var nestedData = new Dictionary<string, object?>
        {
            ["street"] = "123 Main St",
            ["city"] = "Springfield"
        };
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["name"] = "John",
            ["address"] = nestedData
        });

        // Act & Assert
        Assert.That(data.GetPath("address.street"), Is.EqualTo("123 Main St"));
        Assert.That(data.GetPath("address.city"), Is.EqualTo("Springfield"));
        Assert.That(data.GetPath("address.nonexistent"), Is.Null);
        Assert.That(data.GetPath("nonexistent.street"), Is.Null);
    }

    [Test]
    public void NodeEventData_GetPath_WithDeeplyNestedPath_ShouldReturnValue()
    {
        // Arrange
        var deepData = new Dictionary<string, object?>
        {
            ["level1"] = new Dictionary<string, object?>
            {
                ["level2"] = new Dictionary<string, object?>
                {
                    ["level3"] = "deep value"
                }
            }
        };
        var data = NodeEventData.FromDictionary(deepData);

        // Act & Assert
        Assert.That(data.GetPath("level1.level2.level3"), Is.EqualTo("deep value"));
        Assert.That(data.GetPath("level1.level2.nonexistent"), Is.Null);
        Assert.That(data.GetPath("level1.nonexistent.level3"), Is.Null);
    }

    [Test]
    public void NodeEventData_TryGetPath_WithExistingPath_ShouldReturnTrueAndValue()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["user"] = new Dictionary<string, object?>
            {
                ["profile"] = new Dictionary<string, object?>
                {
                    ["name"] = "Alice",
                    ["age"] = 25
                }
            }
        });

        // Act & Assert
        Assert.That(data.TryGetPath("user.profile.name", out var name), Is.True);
        Assert.That(name, Is.EqualTo("Alice"));

        Assert.That(data.TryGetPath<string>("user.profile.name", out var typedName), Is.True);
        Assert.That(typedName, Is.EqualTo("Alice"));

        Assert.That(data.TryGetPath<int>("user.profile.age", out var age), Is.True);
        Assert.That(age, Is.EqualTo(25));
    }

    [Test]
    public void NodeEventData_TryGetPath_WithNonExistingPath_ShouldReturnFalse()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["user"] = new Dictionary<string, object?> { ["name"] = "Bob" }
        });

        // Act & Assert
        Assert.That(data.TryGetPath("user.nonexistent", out var value), Is.False);
        Assert.That(value, Is.Null);

        Assert.That(data.TryGetPath("nonexistent.name", out var value2), Is.False);
        Assert.That(value2, Is.Null);

        Assert.That(data.TryGetPath<string>("user.nonexistent", out var typedValue), Is.False);
        Assert.That(typedValue, Is.Null);
    }

    [Test]
    public void NodeEventData_TryGetPath_WithWrongType_ShouldReturnFalse()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["user"] = new Dictionary<string, object?> { ["age"] = "25" } // string, not int
        });

        // Act & Assert
        Assert.That(data.TryGetPath<int>("user.age", out var age), Is.False);
        Assert.That(age, Is.EqualTo(0));

        Assert.That(data.TryGetPath<string>("user.age", out var ageStr), Is.True);
        Assert.That(ageStr, Is.EqualTo("25"));
    }

    [Test]
    public void NodeEventData_SetPath_WithSimplePath_ShouldCreateNewInstance()
    {
        // Arrange
        var original = NodeEventData.FromDictionary(new Dictionary<string, object?> { ["existing"] = "value" });

        // Act
        var modified = original.SetPath("newKey", "newValue");

        // Assert
        Assert.That(original.ContainsKey("newKey"), Is.False);
        Assert.That(modified.GetPath("newKey"), Is.EqualTo("newValue"));
        Assert.That(modified.GetPath("existing"), Is.EqualTo("value"));
    }

    [Test]
    public void NodeEventData_SetPath_WithNestedPath_ShouldCreateNestedStructure()
    {
        // Arrange
        var original = NodeEventData.Empty;

        // Act
        var modified = original.SetPath("user.profile.name", "Charlie");

        // Assert
        Assert.That(modified.GetPath("user.profile.name"), Is.EqualTo("Charlie"));

        // Verify the nested structure was created
        var userDict = (ImmutableDictionary<string, object?>)modified["user"];
        var profileDict = (ImmutableDictionary<string, object?>)userDict["profile"];
        Assert.That(profileDict["name"], Is.EqualTo("Charlie"));
    }

    [Test]
    public void NodeEventData_SetPath_WithExistingNestedPath_ShouldUpdateValue()
    {
        // Arrange
        var original = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["user"] = new Dictionary<string, object?>
            {
                ["profile"] = new Dictionary<string, object?>
                {
                    ["name"] = "David",
                    ["age"] = 30
                }
            }
        });

        // Act
        var modified = original.SetPath("user.profile.name", "Updated David");

        // Assert
        Assert.That(original.GetPath("user.profile.name"), Is.EqualTo("David"));
        Assert.That(modified.GetPath("user.profile.name"), Is.EqualTo("Updated David"));
        Assert.That(modified.GetPath("user.profile.age"), Is.EqualTo(30)); // Other values preserved
    }

    [Test]
    public void NodeEventData_DotNotation_EdgeCases_ShouldHandleCorrectly()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["empty"] = "",
            ["null"] = null,
            ["zero"] = 0
        });

        // Act & Assert
        Assert.That(data.GetPath(""), Is.Null);
        Assert.That(data.GetPath("empty"), Is.EqualTo(""));
        Assert.That(data.GetPath("null"), Is.Null);
        Assert.That(data.GetPath("zero"), Is.EqualTo(0));

        Assert.That(data.TryGetPath("", out var emptyPath), Is.False);
        Assert.That(data.TryGetPath("empty", out var emptyValue), Is.True);
        Assert.That(emptyValue, Is.EqualTo(""));

        // Test setting with empty path should throw
        Assert.Throws<ArgumentException>(() => data.SetPath("", "value"));
        Assert.Throws<ArgumentException>(() => data.SetPath(null!, "value"));
    }

    [Test]
    public void NodeEventData_DotNotation_WithArrays_ShouldWork()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["items"] = new List<object?> { "first", "second", "third" },
            ["nested"] = new Dictionary<string, object?>
            {
                ["array"] = new[] { 1, 2, 3 }
            }
        });

        // Act & Assert
        var items = data.GetPath("items");
        Assert.That(items, Is.Not.Null);
        Assert.That(items, Is.TypeOf<ImmutableArray<object?>>());

        var nestedArray = data.GetPath("nested.array");
        Assert.That(nestedArray, Is.Not.Null);
        Assert.That(nestedArray, Is.TypeOf<ImmutableArray<object?>>());
    }

    [Test]
    public void NodeEventData_DotNotation_WithComplexMixedTypes_ShouldWork()
    {
        // Arrange
        var complexData = new Dictionary<string, object?>
        {
            ["metadata"] = new Dictionary<string, object?>
            {
                ["version"] = "1.0",
                ["tags"] = new[] { "production", "api" },
                ["config"] = new Dictionary<string, object?>
                {
                    ["timeout"] = 30000,
                    ["retries"] = 3,
                    ["endpoints"] = new Dictionary<string, object?>
                    {
                        ["primary"] = "https://api.example.com",
                        ["fallback"] = "https://backup.example.com"
                    }
                }
            }
        };
        var data = NodeEventData.FromDictionary(complexData);

        // Act & Assert
        Assert.That(data.GetPath("metadata.version"), Is.EqualTo("1.0"));
        Assert.That(data.GetPath("metadata.config.timeout"), Is.EqualTo(30000));
        Assert.That(data.GetPath("metadata.config.endpoints.primary"), Is.EqualTo("https://api.example.com"));

        Assert.That(data.TryGetPath<string>("metadata.config.endpoints.fallback", out var fallback), Is.True);
        Assert.That(fallback, Is.EqualTo("https://backup.example.com"));

        Assert.That(data.TryGetPath<int>("metadata.config.retries", out var retries), Is.True);
        Assert.That(retries, Is.EqualTo(3));
    }

    [Test]
    public void NodeEventData_SetPaths_WithKeyValuePairs_ShouldSetMultipleValues()
    {
        // Arrange
        var original = NodeEventData.Empty;
        var pathValues = new[]
        {
            new KeyValuePair<string, object?>("user.name", "Alice"),
            new KeyValuePair<string, object?>("user.age", 30),
            new KeyValuePair<string, object?>("user.profile.email", "<EMAIL>"),
            new KeyValuePair<string, object?>("settings.theme", "dark"),
            new KeyValuePair<string, object?>("settings.notifications.email", true),
            new KeyValuePair<string, object?>("settings.notifications.sms", false)
        };

        // Act
        var modified = original.SetPaths(pathValues);

        // Assert
        Assert.That(modified.GetPath("user.name"), Is.EqualTo("Alice"));
        Assert.That(modified.GetPath("user.age"), Is.EqualTo(30));
        Assert.That(modified.GetPath("user.profile.email"), Is.EqualTo("<EMAIL>"));
        Assert.That(modified.GetPath("settings.theme"), Is.EqualTo("dark"));
        Assert.That(modified.GetPath("settings.notifications.email"), Is.EqualTo(true));
        Assert.That(modified.GetPath("settings.notifications.sms"), Is.EqualTo(false));
    }

    [Test]
    public void NodeEventData_SetPaths_WithAnonymousObject_ShouldSetValues()
    {
        // Arrange
        var original = NodeEventData.Empty;

        // Act
        var modified = original.SetPaths(new
        {
            Name = "Bob",
            Age = 25,
            Email = "<EMAIL>",
            IsActive = true
        });

        // Assert
        Assert.That(modified.GetPath("Name"), Is.EqualTo("Bob"));
        Assert.That(modified.GetPath("Age"), Is.EqualTo(25));
        Assert.That(modified.GetPath("Email"), Is.EqualTo("<EMAIL>"));
        Assert.That(modified.GetPath("IsActive"), Is.EqualTo(true));
    }

    [Test]
    public void NodeEventData_SetPaths_WithExistingData_ShouldMergeCorrectly()
    {
        // Arrange
        var original = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["user"] = new Dictionary<string, object?>
            {
                ["name"] = "Charlie",
                ["profile"] = new Dictionary<string, object?>
                {
                    ["email"] = "<EMAIL>",
                    ["phone"] = "************"
                }
            },
            ["settings"] = new Dictionary<string, object?> { ["theme"] = "light" }
        });

        var pathValues = new[]
        {
            new KeyValuePair<string, object?>("user.profile.email", "<EMAIL>"),
            new KeyValuePair<string, object?>("user.profile.address", "123 Main St"),
            new KeyValuePair<string, object?>("settings.language", "en-US"),
            new KeyValuePair<string, object?>("metadata.version", "2.0")
        };

        // Act
        var modified = original.SetPaths(pathValues);

        // Assert
        // Updated values
        Assert.That(modified.GetPath("user.profile.email"), Is.EqualTo("<EMAIL>"));
        Assert.That(modified.GetPath("user.profile.address"), Is.EqualTo("123 Main St"));
        Assert.That(modified.GetPath("settings.language"), Is.EqualTo("en-US"));
        Assert.That(modified.GetPath("metadata.version"), Is.EqualTo("2.0"));

        // Preserved values
        Assert.That(modified.GetPath("user.name"), Is.EqualTo("Charlie"));
        Assert.That(modified.GetPath("user.profile.phone"), Is.EqualTo("************"));
        Assert.That(modified.GetPath("settings.theme"), Is.EqualTo("light"));
    }

    [Test]
    public void NodeEventData_SetPaths_WithNullArgument_ShouldThrowArgumentNullException()
    {
        // Arrange
        var data = NodeEventData.Empty;

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => data.SetPaths((IEnumerable<KeyValuePair<string, object?>>)null!));
        Assert.Throws<ArgumentNullException>(() => data.SetPaths((object)null!));
    }

    [Test]
    public void NodeEventData_SetPaths_WithEmptyOrNullPaths_ShouldThrowArgumentException()
    {
        // Arrange
        var data = NodeEventData.Empty;
        var invalidPaths = new[]
        {
            new KeyValuePair<string, object?>("", "value"),
            new KeyValuePair<string, object?>(null!, "value")
        };

        // Act & Assert
        Assert.Throws<ArgumentException>(() => data.SetPaths(invalidPaths));
    }

    [Test]
    public void NodeEventData_SetPaths_ShouldBeMoreEfficientThanChainedSetPath()
    {
        // This test demonstrates the usage pattern - efficiency would be measured in performance tests
        // Arrange
        var original = NodeEventData.Empty;
        var pathValues = new[]
        {
            new KeyValuePair<string, object?>("api.endpoints.users.get", "/api/v1/users"),
            new KeyValuePair<string, object?>("api.endpoints.users.post", "/api/v1/users"),
            new KeyValuePair<string, object?>("api.endpoints.orders.get", "/api/v1/orders"),
            new KeyValuePair<string, object?>("api.config.timeout", 30000),
            new KeyValuePair<string, object?>("api.config.retries", 3)
        };

        // Act - Single operation
        var resultSetPaths = original.SetPaths(pathValues);

        // Act - Chained operations (for comparison)
        var resultChained = original
            .SetPath("api.endpoints.users.get", "/api/v1/users")
            .SetPath("api.endpoints.users.post", "/api/v1/users")
            .SetPath("api.endpoints.orders.get", "/api/v1/orders")
            .SetPath("api.config.timeout", 30000)
            .SetPath("api.config.retries", 3);

        // Assert - Both should produce the same result
        Assert.That(resultSetPaths.GetPath("api.endpoints.users.get"), Is.EqualTo(resultChained.GetPath("api.endpoints.users.get")));
        Assert.That(resultSetPaths.GetPath("api.endpoints.users.post"), Is.EqualTo(resultChained.GetPath("api.endpoints.users.post")));
        Assert.That(resultSetPaths.GetPath("api.endpoints.orders.get"), Is.EqualTo(resultChained.GetPath("api.endpoints.orders.get")));
        Assert.That(resultSetPaths.GetPath("api.config.timeout"), Is.EqualTo(resultChained.GetPath("api.config.timeout")));
        Assert.That(resultSetPaths.GetPath("api.config.retries"), Is.EqualTo(resultChained.GetPath("api.config.retries")));
    }
}

[TestFixture]
public class NCalcExpressionTests
{
    private const string TestNodeName = "TestNode";

    [Test]
    public void NCalcExpression_BasicPropertyAccess_ShouldWork()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["user"] = new Dictionary<string, object?>
            {
                ["name"] = "John Doe",
                ["age"] = 30
            }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test.topic");

        // Act & Assert
        var nameResult = EvaluateExpression("[data.user.name]", nodeEvent);
        var ageResult = EvaluateExpression("[data.user.age]", nodeEvent);
        var nodeResult = EvaluateExpression("node", nodeEvent);
        var topicResult = EvaluateExpression("topic", nodeEvent);

        Assert.That(nameResult, Is.EqualTo("John Doe"));
        Assert.That(ageResult, Is.EqualTo(30));
        Assert.That(nodeResult, Is.EqualTo(TestNodeName));
        Assert.That(topicResult, Is.EqualTo("test.topic"));
    }

    [Test]
    public void NCalcExpression_NodeEventProperties_ShouldWork()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["value"] = 42
        });
        var nodeEvent = new NodeEvent("SensorNode", data, "sensor.reading");

        // Act & Assert
        var nodeResult = EvaluateExpression("node", nodeEvent);
        var topicResult = EvaluateExpression("topic", nodeEvent);
        var timestampResult = EvaluateExpression("timestamp", nodeEvent);
        var idResult = EvaluateExpression("id", nodeEvent);
        var dataValueResult = EvaluateExpression("[data.value]", nodeEvent);

        Assert.That(nodeResult, Is.EqualTo("SensorNode"));
        Assert.That(topicResult, Is.EqualTo("sensor.reading"));
        Assert.That(timestampResult, Is.TypeOf<DateTimeOffset>());
        Assert.That(idResult, Is.TypeOf<Guid>());
        Assert.That(dataValueResult, Is.EqualTo(42));
    }

    [Test]
    public void NCalcExpression_MathematicalCalculations_ShouldWork()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["order"] = new Dictionary<string, object?>
            {
                ["amount"] = 100.0,
                ["tax_rate"] = 0.08
            }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data);

        // Act & Assert
        var taxResult = EvaluateExpression("[data.order.amount] * [data.order.tax_rate]", nodeEvent);
        var totalResult = EvaluateExpression("[data.order.amount] + ([data.order.amount] * [data.order.tax_rate])", nodeEvent);

        Assert.That(taxResult, Is.EqualTo(8.0));
        Assert.That(totalResult, Is.EqualTo(108.0));
    }

    [Test]
    public void NCalcExpression_StringFunctions_ShouldWork()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["user"] = new Dictionary<string, object?>
            {
                ["first_name"] = "john",
                ["last_name"] = "doe",
                ["email"] = "<EMAIL>"
            }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data);

        // Act & Assert
        var upperResult = EvaluateExpression("upper([data.user.first_name])", nodeEvent);
        var concatResult = EvaluateExpression("concat([data.user.first_name], ' ', [data.user.last_name])", nodeEvent);
        var substringResult = EvaluateExpression("substring([data.user.email], 0, indexOf([data.user.email], '@'))", nodeEvent);

        Assert.That(upperResult, Is.EqualTo("JOHN"));
        Assert.That(concatResult, Is.EqualTo("john doe"));
        Assert.That(substringResult, Is.EqualTo("john.doe"));
    }

    [Test]
    public void NCalcExpression_ConditionalLogic_ShouldWork()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["user"] = new Dictionary<string, object?>
            {
                ["age"] = 25,
                ["active"] = true
            },
            ["order"] = new Dictionary<string, object?>
            {
                ["amount"] = 1500.0
            }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data);

        // Act & Assert
        var ageCheckResult = EvaluateExpression("[data.user.age] >= 18", nodeEvent);
        var categoryResult = EvaluateExpression("[data.order.amount] > 1000 ? 'Premium' : 'Standard'", nodeEvent);
        var eligibilityResult = EvaluateExpression("[data.user.active] && [data.user.age] > 21", nodeEvent);

        Assert.That(ageCheckResult, Is.EqualTo(true));
        Assert.That(categoryResult, Is.EqualTo("Premium"));
        Assert.That(eligibilityResult, Is.EqualTo(true));
    }

    [Test]
    public void NCalcExpression_NullHandling_ShouldWork()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["user"] = new Dictionary<string, object?>
            {
                ["name"] = "John",
                ["bonus"] = null
            }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data);

        // Act & Assert
        var nvlResult = EvaluateExpression("nvl([data.user.bonus], 0)", nodeEvent);
        var isNullResult = EvaluateExpression("isnull([data.user.bonus])", nodeEvent);
        var nzsResult = EvaluateExpression("nzs([data.user.nickname], [data.user.name])", nodeEvent);

        Assert.That(nvlResult, Is.EqualTo(0));
        Assert.That(isNullResult, Is.EqualTo(true));
        Assert.That(nzsResult, Is.EqualTo("John"));
    }

    [Test]
    public void NCalcExpression_DotNotationFunctions_ShouldWork()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["user"] = new Dictionary<string, object?>
            {
                ["profile"] = new Dictionary<string, object?>
                {
                    ["name"] = "Alice",
                    ["department"] = "Engineering"
                }
            }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data);

        // Act & Assert
        var getPathResult = EvaluateExpression("getPath(data, 'user.profile.name')", nodeEvent);
        var hasPathTrueResult = EvaluateExpression("hasPath(data, 'user.profile.department')", nodeEvent);
        var hasPathFalseResult = EvaluateExpression("hasPath(data, 'user.profile.nonexistent')", nodeEvent);

        Assert.That(getPathResult, Is.EqualTo("Alice"));
        Assert.That(hasPathTrueResult, Is.EqualTo(true));
        Assert.That(hasPathFalseResult, Is.EqualTo(false));
    }

    [Test]
    public void NCalcExpression_MathFunctions_ShouldWork()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["values"] = new Dictionary<string, object?>
            {
                ["price"] = 123.456,
                ["negative"] = -42.7
            }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data);

        // Act & Assert
        var roundResult = EvaluateExpression("round([data.values.price], 2)", nodeEvent);
        var absResult = EvaluateExpression("abs([data.values.negative])", nodeEvent);
        var floorResult = EvaluateExpression("floor([data.values.price])", nodeEvent);

        Assert.That(roundResult, Is.EqualTo(123.46));
        Assert.That(absResult, Is.EqualTo(42.7));
        Assert.That(floorResult, Is.EqualTo(123.0));
    }

    private static object? EvaluateExpression(string expression, NodeEvent nodeEvent)
    {
        var expr = KegBridgeCore.Services.EventNodeRuleExpression.Create(expression);
        if (expr == null) return null;
        return KegBridgeCore.Services.EventNodeRuleExpression.Evaluate(expr, nodeEvent);
    }
}

[TestFixture]
public class NodeEventByteArrayExtractionTests
{
    private const string TestNodeName = "TestNode";

    [Test]
    public void GetUInt16LE_ShouldExtractLittleEndianUInt16()
    {
        // Arrange - bytes representing 0x1234 in little endian (0x34, 0x12)
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["bytes"] = new byte[] { 0x34, 0x12, 0xFF, 0xAA }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act
        var result = EvaluateExpression("getUInt16LE([data.bytes])", nodeEvent);

        // Assert
        Assert.That(result, Is.EqualTo(0x1234u));
    }

    [Test]
    public void GetUInt16BE_ShouldExtractBigEndianUInt16()
    {
        // Arrange - bytes representing 0x1234 in big endian (0x12, 0x34)
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["bytes"] = new byte[] { 0x12, 0x34, 0xFF, 0xAA }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act
        var result = EvaluateExpression("getUInt16BE([data.bytes])", nodeEvent);

        // Assert
        Assert.That(result, Is.EqualTo(0x1234u));
    }

    [Test]
    public void GetUInt16LE_WithOffset_ShouldExtractFromCorrectPosition()
    {
        // Arrange - 0x5678 at offset 2 in little endian
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["bytes"] = new byte[] { 0xFF, 0xAA, 0x78, 0x56, 0xBB, 0xCC }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act
        var result = EvaluateExpression("getUInt16LE([data.bytes], 2)", nodeEvent);

        // Assert
        Assert.That(result, Is.EqualTo(0x5678u));
    }

    [Test]
    public void GetUInt32LE_ShouldExtractLittleEndianUInt32()
    {
        // Arrange - bytes representing 0x12345678 in little endian (0x78, 0x56, 0x34, 0x12)
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["bytes"] = new byte[] { 0x78, 0x56, 0x34, 0x12, 0xFF, 0xAA }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act
        var result = EvaluateExpression("getUInt32LE([data.bytes])", nodeEvent);

        // Assert
        Assert.That(result, Is.EqualTo(0x12345678u));
    }

    [Test]
    public void GetUInt32BE_ShouldExtractBigEndianUInt32()
    {
        // Arrange - bytes representing 0x12345678 in big endian (0x12, 0x34, 0x56, 0x78)
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["bytes"] = new byte[] { 0x12, 0x34, 0x56, 0x78, 0xFF, 0xAA }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act
        var result = EvaluateExpression("getUInt32BE([data.bytes])", nodeEvent);

        // Assert
        Assert.That(result, Is.EqualTo(0x12345678u));
    }

    [Test]
    public void GetUInt32LE_WithOffset_ShouldExtractFromCorrectPosition()
    {
        // Arrange - 0xAABBCCDD at offset 1 in little endian
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["bytes"] = new byte[] { 0xFF, 0xDD, 0xCC, 0xBB, 0xAA, 0x11, 0x22 }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act
        var result = EvaluateExpression("getUInt32LE([data.bytes], 1)", nodeEvent);

        // Assert
        Assert.That(result, Is.EqualTo(0xAABBCCDDu));
    }

    [Test]
    public void GetUInt16_WithExplicitEndianness_ShouldWork()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["bytes"] = new byte[] { 0x12, 0x34 }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act & Assert - Little endian (default)
        var resultLE = EvaluateExpression("getUInt16([data.bytes], 0, true)", nodeEvent);
        Assert.That(resultLE, Is.EqualTo(0x3412u));

        // Act & Assert - Big endian
        var resultBE = EvaluateExpression("getUInt16([data.bytes], 0, false)", nodeEvent);
        Assert.That(resultBE, Is.EqualTo(0x1234u));
    }

    [Test]
    public void GetUInt32_WithExplicitEndianness_ShouldWork()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["bytes"] = new byte[] { 0x12, 0x34, 0x56, 0x78 }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act & Assert - Little endian (default)
        var resultLE = EvaluateExpression("getUInt32([data.bytes], 0, true)", nodeEvent);
        Assert.That(resultLE, Is.EqualTo(0x78563412u));

        // Act & Assert - Big endian
        var resultBE = EvaluateExpression("getUInt32([data.bytes], 0, false)", nodeEvent);
        Assert.That(resultBE, Is.EqualTo(0x12345678u));
    }

    [Test]
    public void GetUInt16_InsufficientBytes_ShouldThrowException()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["bytes"] = new byte[] { 0x12 } // Only 1 byte, need 2
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act & Assert
        Assert.Throws<ArgumentException>(() => EvaluateExpression("getUInt16LE([data.bytes])", nodeEvent));
    }

    [Test]
    public void GetUInt32_InsufficientBytes_ShouldThrowException()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["bytes"] = new byte[] { 0x12, 0x34, 0x56 } // Only 3 bytes, need 4
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act & Assert
        Assert.Throws<ArgumentException>(() => EvaluateExpression("getUInt32LE([data.bytes])", nodeEvent));
    }

    [Test]
    public void GetUInt16_NegativeOffset_ShouldExtractFromEnd()
    {
        // Arrange - 6 bytes: [0x11, 0x22, 0x33, 0x44, 0x55, 0x66]
        // Offset -2 should extract the last 2 bytes: 0x55, 0x66
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["bytes"] = new byte[] { 0x11, 0x22, 0x33, 0x44, 0x55, 0x66 }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act
        var result = EvaluateExpression("getUInt16LE([data.bytes], -2)", nodeEvent);

        // Assert - Little endian: 0x66, 0x55 -> 0x5566
        Assert.That(result, Is.EqualTo(0x6655u));
    }

    [Test]
    public void GetUInt16_NegativeOffsetBigEndian_ShouldExtractFromEnd()
    {
        // Arrange - 6 bytes: [0x11, 0x22, 0x33, 0x44, 0x55, 0x66]
        // Offset -2 should extract the last 2 bytes: 0x55, 0x66
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["bytes"] = new byte[] { 0x11, 0x22, 0x33, 0x44, 0x55, 0x66 }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act
        var result = EvaluateExpression("getUInt16BE([data.bytes], -2)", nodeEvent);

        // Assert - Big endian: 0x55, 0x66 -> 0x5566
        Assert.That(result, Is.EqualTo(0x5566u));
    }

    [Test]
    public void GetUInt32_NegativeOffset_ShouldExtractFromEnd()
    {
        // Arrange - 8 bytes: [0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88]
        // Offset -4 should extract the last 4 bytes: 0x55, 0x66, 0x77, 0x88
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["bytes"] = new byte[] { 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88 }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act
        var result = EvaluateExpression("getUInt32LE([data.bytes], -4)", nodeEvent);

        // Assert - Little endian: 0x88, 0x77, 0x66, 0x55 -> 0x55667788
        Assert.That(result, Is.EqualTo(0x88776655u));
    }

    [Test]
    public void GetUInt32_NegativeOffsetBigEndian_ShouldExtractFromEnd()
    {
        // Arrange - 8 bytes: [0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88]
        // Offset -4 should extract the last 4 bytes: 0x55, 0x66, 0x77, 0x88
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["bytes"] = new byte[] { 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88 }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act
        var result = EvaluateExpression("getUInt32BE([data.bytes], -4)", nodeEvent);

        // Assert - Big endian: 0x55, 0x66, 0x77, 0x88 -> 0x55667788
        Assert.That(result, Is.EqualTo(0x55667788u));
    }

    [Test]
    public void GetUInt16_NegativeOffsetMiddle_ShouldExtractCorrectly()
    {
        // Arrange - 6 bytes: [0x11, 0x22, 0x33, 0x44, 0x55, 0x66]
        // Offset -4 should extract bytes at index 2,3: 0x33, 0x44
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["bytes"] = new byte[] { 0x11, 0x22, 0x33, 0x44, 0x55, 0x66 }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act
        var result = EvaluateExpression("getUInt16LE([data.bytes], -4)", nodeEvent);

        // Assert - Little endian: 0x44, 0x33 -> 0x3344
        Assert.That(result, Is.EqualTo(0x4433u));
    }

    [Test]
    public void GetUInt16_OffsetTooLarge_ShouldThrowException()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["bytes"] = new byte[] { 0x12, 0x34 }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act & Assert
        Assert.Throws<ArgumentException>(() => EvaluateExpression("getUInt16LE([data.bytes], 2)", nodeEvent));
    }

    [Test]
    public void GetUInt16_NegativeOffsetTooLarge_ShouldThrowException()
    {
        // Arrange - 4 bytes, but asking for offset -6 (would be index -2)
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["bytes"] = new byte[] { 0x11, 0x22, 0x33, 0x44 }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act & Assert
        Assert.Throws<ArgumentException>(() => EvaluateExpression("getUInt16LE([data.bytes], -6)", nodeEvent));
    }

    [Test]
    public void GetUInt32_NegativeOffsetTooLarge_ShouldThrowException()
    {
        // Arrange - 4 bytes, but asking for offset -6 (would be index -2)
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["bytes"] = new byte[] { 0x11, 0x22, 0x33, 0x44 }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act & Assert
        Assert.Throws<ArgumentException>(() => EvaluateExpression("getUInt32LE([data.bytes], -6)", nodeEvent));
    }

    [Test]
    public void GetUInt16_NegativeOffsetInsufficientBytes_ShouldThrowException()
    {
        // Arrange - 4 bytes, offset -1 would need bytes at index 3,4 but only have 0,1,2,3
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["bytes"] = new byte[] { 0x11, 0x22, 0x33, 0x44 }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act & Assert
        Assert.Throws<ArgumentException>(() => EvaluateExpression("getUInt16LE([data.bytes], -1)", nodeEvent));
    }

    [Test]
    public void GetUInt16_EmptyByteArray_ShouldThrowException()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["bytes"] = new byte[0]
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act & Assert
        Assert.Throws<ArgumentException>(() => EvaluateExpression("getUInt16LE([data.bytes])", nodeEvent));
    }

    [Test]
    public void GetUInt16_NonByteArrayInput_ShouldThrowException()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["notBytes"] = "not a byte array"
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act & Assert
        Assert.Throws<ArgumentException>(() => EvaluateExpression("getUInt16LE([data.notBytes])", nodeEvent));
    }
    
    private static object? EvaluateExpression(string expression, NodeEvent nodeEvent)
    {
        var expr = KegBridgeCore.Services.EventNodeRuleExpression.Create(expression);
        if (expr == null) return null;
        return KegBridgeCore.Services.EventNodeRuleExpression.Evaluate(expr, nodeEvent);
    }
}

[TestFixture]
public class NodeEventDateTimeExtractionTests
{
    private const string TestNodeName = "TestNode";

    [Test]
    public void GetDateTimeLE_ShouldExtractLittleEndianDateTime()
    {
        // Arrange - Unix timestamp in nanoseconds (little endian): 1755872665095018066
        // This represents approximately 2025-08-22 13:37:45.095018066 UTC
        var epochNanos = 1755872665095018066UL;
        var bytes = BitConverter.GetBytes(epochNanos);
        if (!BitConverter.IsLittleEndian)
        {
            Array.Reverse(bytes);
        }

        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["timestamp_bytes"] = bytes
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act
        var result = EvaluateExpression("getDateTimeLE([data.timestamp_bytes])", nodeEvent);

        // Assert
        Assert.That(result, Is.TypeOf<DateTime>());
        var dateTime = (DateTime)result;
        Assert.That(dateTime.Year, Is.EqualTo(2025));
        Assert.That(dateTime.Month, Is.EqualTo(8));
        Assert.That(dateTime.Day, Is.EqualTo(22));
    }

    [Test]
    public void GetDateTimeBE_ShouldExtractBigEndianDateTime()
    {
        // Arrange - Unix timestamp in nanoseconds (big endian)
        var epochNanos = 1755872665095018066UL;
        var bytes = BitConverter.GetBytes(epochNanos);
        if (BitConverter.IsLittleEndian)
        {
            Array.Reverse(bytes);
        }

        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["timestamp_bytes"] = bytes
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act
        var result = EvaluateExpression("getDateTimeBE([data.timestamp_bytes])", nodeEvent);

        // Assert
        Assert.That(result, Is.TypeOf<DateTime>());
        var dateTime = (DateTime)result;
        Assert.That(dateTime.Year, Is.EqualTo(2025));
        Assert.That(dateTime.Month, Is.EqualTo(8));
        Assert.That(dateTime.Day, Is.EqualTo(22));
    }

    [Test]
    public void GetDateTime_WithOffset_ShouldExtractFromCorrectPosition()
    {
        // Arrange - timestamp at offset 4
        var epochNanos = 1755872665095018066UL;
        var timestampBytes = BitConverter.GetBytes(epochNanos);
        if (!BitConverter.IsLittleEndian)
        {
            Array.Reverse(timestampBytes);
        }

        var bytes = new byte[16];
        Array.Copy(timestampBytes, 0, bytes, 4, 8); // Place timestamp at offset 4

        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["data_with_timestamp"] = bytes
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act
        var result = EvaluateExpression("getDateTimeLE([data.data_with_timestamp], 4)", nodeEvent);

        // Assert
        Assert.That(result, Is.TypeOf<DateTime>());
        var dateTime = (DateTime)result;
        Assert.That(dateTime.Year, Is.EqualTo(2025));
    }

    [Test]
    public void GetDateTime_WithNegativeOffset_ShouldExtractFromEnd()
    {
        // Arrange - timestamp at the end of a 16-byte array
        var epochNanos = 1755872665095018066UL;
        var timestampBytes = BitConverter.GetBytes(epochNanos);
        if (!BitConverter.IsLittleEndian)
        {
            Array.Reverse(timestampBytes);
        }

        var bytes = new byte[16];
        Array.Copy(timestampBytes, 0, bytes, 8, 8); // Place timestamp at the end

        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["data_with_timestamp"] = bytes
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act
        var result = EvaluateExpression("getDateTimeLE([data.data_with_timestamp], -8)", nodeEvent);

        // Assert
        Assert.That(result, Is.TypeOf<DateTime>());
        var dateTime = (DateTime)result;
        Assert.That(dateTime.Year, Is.EqualTo(2025));
    }

    [Test]
    public void GetDateTime_InsufficientBytes_ShouldThrowException()
    {
        // Arrange - Only 4 bytes, need 8
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["short_bytes"] = new byte[] { 0x01, 0x02, 0x03, 0x04 }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act & Assert
        Assert.Throws<ArgumentException>(() => EvaluateExpression("getDateTimeLE([data.short_bytes])", nodeEvent));
    }

    [Test]
    public void GetDateTime_DifferentEpochFormats_ShouldDetectCorrectly()
    {
        // Test different epoch formats
        var testCases = new[]
        {
            // Unix timestamp in seconds (current time)
            new { Value = 1692710265UL, Description = "Unix seconds" },

            // Unix timestamp in milliseconds
            new { Value = 1692710265000UL, Description = "Unix milliseconds" },

            // Unix timestamp in microseconds
            new { Value = 1692710265000000UL, Description = "Unix microseconds" },

            // Unix timestamp in nanoseconds
            new { Value = 1692710265000000000UL, Description = "Unix nanoseconds" }
        };

        foreach (var testCase in testCases)
        {
            // Arrange
            var bytes = BitConverter.GetBytes(testCase.Value);
            var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
            {
                ["timestamp"] = bytes
            });
            var nodeEvent = new NodeEvent(TestNodeName, data, "test");

            // Act
            var result = EvaluateExpression("getDateTimeLE([data.timestamp])", nodeEvent);

            // Assert
            Assert.That(result, Is.TypeOf<DateTime>(), $"Failed for {testCase.Description}");
            var dateTime = (DateTime)result;

            // Should be a reasonable date (between 1970 and 2100)
            Assert.That(dateTime.Year, Is.GreaterThanOrEqualTo(1970), $"Year too early for {testCase.Description}");
            Assert.That(dateTime.Year, Is.LessThanOrEqualTo(2100), $"Year too late for {testCase.Description}");
        }
    }

    [Test]
    public void GetDateTime_SubsecondPrecision_ShouldPreserveAccuracy()
    {
        // Test nanosecond precision: 1755872665095018066 nanoseconds
        // This should be: 2025-08-22 14:24:25.095018066 UTC (with nanosecond precision)
        var nanoseconds = 1755872665095018066UL;
        var bytes = BitConverter.GetBytes(nanoseconds);

        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["timestamp_nanos"] = bytes
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act
        var result = EvaluateExpression("getDateTimeLE([data.timestamp_nanos])", nodeEvent);

        // Assert
        Assert.That(result, Is.TypeOf<DateTime>());
        var dateTime = (DateTime)result;

        // Check the basic date/time components
        Assert.That(dateTime.Year, Is.EqualTo(2025));
        Assert.That(dateTime.Month, Is.EqualTo(8));
        Assert.That(dateTime.Day, Is.EqualTo(22));
        Assert.That(dateTime.Hour, Is.EqualTo(14));
        Assert.That(dateTime.Minute, Is.EqualTo(24));
        Assert.That(dateTime.Second, Is.EqualTo(25));

        // Check subsecond precision (should preserve at least millisecond precision)
        // The original nanoseconds: 1755872665095018066
        // Expected milliseconds: 095 (from .095018066)
        Assert.That(dateTime.Millisecond, Is.EqualTo(95), "Millisecond precision should be preserved");

        // Check that we have some subsecond precision beyond milliseconds
        // .NET DateTime has 100-nanosecond (tick) precision
        var expectedTicks = nanoseconds / 100UL; // Convert nanoseconds to ticks
        var unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        var expectedDateTime = unixEpoch.AddTicks((long)expectedTicks);

        // The extracted DateTime should match the expected DateTime with tick precision
        Assert.That(dateTime.Ticks, Is.EqualTo(expectedDateTime.Ticks), "Tick precision should be preserved");
    }

    [Test]
    public void GetDateTime_MicrosecondPrecision_ShouldPreserveAccuracy()
    {
        // Test microsecond precision: 1755872665095018 microseconds
        // This should preserve microsecond precision
        var microseconds = 1755872665095018UL;
        var bytes = BitConverter.GetBytes(microseconds);

        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["timestamp_micros"] = bytes
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act
        var result = EvaluateExpression("getDateTimeLE([data.timestamp_micros])", nodeEvent);

        // Assert
        Assert.That(result, Is.TypeOf<DateTime>());
        var dateTime = (DateTime)result;

        // Check basic components
        Assert.That(dateTime.Year, Is.EqualTo(2025));
        Assert.That(dateTime.Month, Is.EqualTo(8));
        Assert.That(dateTime.Day, Is.EqualTo(22));

        // Check that microsecond precision is preserved
        var expectedTicks = microseconds * 10UL; // Convert microseconds to ticks
        var unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        var expectedDateTime = unixEpoch.AddTicks((long)expectedTicks);

        Assert.That(dateTime.Ticks, Is.EqualTo(expectedDateTime.Ticks), "Microsecond precision should be preserved");
    }

    [Test]
    public void GetDateTime_MillisecondPrecision_ShouldPreserveAccuracy()
    {
        // Test millisecond precision: Use a value that will be clearly detected as milliseconds
        // Use a value well within the millisecond range (> 10^9 and <= 10^12)
        var milliseconds = 1700000000123UL; // This is clearly in the millisecond range (13 digits, > 10^12)
        var bytes = BitConverter.GetBytes(milliseconds);

        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["timestamp_millis"] = bytes
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test");

        // Act
        var result = EvaluateExpression("getDateTimeLE([data.timestamp_millis])", nodeEvent);

        // Assert
        Assert.That(result, Is.TypeOf<DateTime>());
        var dateTime = (DateTime)result;

        // Check that it's a reasonable date (should be around 2023)
        Assert.That(dateTime.Year, Is.GreaterThanOrEqualTo(2020));
        Assert.That(dateTime.Year, Is.LessThanOrEqualTo(2030));

        // Check exact tick precision
        var expectedTicks = milliseconds * 10000UL; // Convert milliseconds to ticks
        var unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        var expectedDateTime = unixEpoch.AddTicks((long)expectedTicks);

        Assert.That(dateTime.Ticks, Is.EqualTo(expectedDateTime.Ticks), "Millisecond precision should be preserved");

        // The millisecond component should match the expected value
        Assert.That(dateTime.Millisecond, Is.EqualTo(expectedDateTime.Millisecond), "Millisecond component should match expected");

        // Verify the millisecond component specifically (123 from the input 1700000000123)
        Assert.That(dateTime.Millisecond, Is.EqualTo(123), "Should preserve the 123 milliseconds from input");
    }

    private static object? EvaluateExpression(string expression, NodeEvent nodeEvent)
    {
        var expr = KegBridgeCore.Services.EventNodeRuleExpression.Create(expression);
        if (expr == null) return null;
        return KegBridgeCore.Services.EventNodeRuleExpression.Evaluate(expr, nodeEvent);
    }
}

[TestFixture]
public class NodeEventDeletePathTests
{
    private const string TestNodeName = "TestNode";

    [Test]
    public void DeletePath_SimpleLeafNode_ShouldRemoveProperty()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["name"] = "John",
            ["age"] = 30,
            ["active"] = true
        });

        // Act
        var result = data.DeletePath("age");

        // Assert
        Assert.That(result.ContainsKey("name"), Is.True);
        Assert.That(result.ContainsKey("active"), Is.True);
        Assert.That(result.ContainsKey("age"), Is.False);
        Assert.That(result["name"], Is.EqualTo("John"));
        Assert.That(result["active"], Is.EqualTo(true));
    }

    [Test]
    public void DeletePath_NestedLeafNode_ShouldRemoveProperty()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["user"] = new Dictionary<string, object?>
            {
                ["profile"] = new Dictionary<string, object?>
                {
                    ["name"] = "John",
                    ["age"] = 30,
                    ["email"] = "<EMAIL>"
                }
            }
        });

        // Act
        var result = data.DeletePath("user.profile.age");

        // Assert
        Assert.That(result.GetPath("user.profile.name"), Is.EqualTo("John"));
        Assert.That(result.GetPath("user.profile.email"), Is.EqualTo("<EMAIL>"));
        Assert.That(result.TryGetPath("user.profile.age", out _), Is.False);
    }

    [Test]
    public void DeletePath_NonLeafNodeWithoutPermission_ShouldThrowException()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["user"] = new Dictionary<string, object?>
            {
                ["profile"] = new Dictionary<string, object?>
                {
                    ["name"] = "John",
                    ["age"] = 30
                }
            }
        });

        // Act & Assert
        var ex = Assert.Throws<ArgumentException>(() => data.DeletePath("user.profile"));
        Assert.That(ex.Message, Does.Contain("not a leaf node"));
    }

    [Test]
    public void DeletePath_NonLeafNodeWithPermission_ShouldRemoveEntireSubtree()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["user"] = new Dictionary<string, object?>
            {
                ["profile"] = new Dictionary<string, object?>
                {
                    ["name"] = "John",
                    ["age"] = 30
                },
                ["settings"] = new Dictionary<string, object?>
                {
                    ["theme"] = "dark"
                }
            }
        });

        // Act
        var result = data.DeletePath("user.profile", allowNonLeafDeletion: true);

        // Assert
        Assert.That(result.TryGetPath("user.profile", out _), Is.False);
        Assert.That(result.TryGetPath("user.profile.name", out _), Is.False);
        Assert.That(result.TryGetPath("user.profile.age", out _), Is.False);
        Assert.That(result.GetPath("user.settings.theme"), Is.EqualTo("dark")); // Other branches should remain
    }

    [Test]
    public void DeletePath_NonExistentPath_ShouldThrowException()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["user"] = new Dictionary<string, object?>
            {
                ["name"] = "John"
            }
        });

        // Act & Assert
        var ex = Assert.Throws<InvalidOperationException>(() => data.DeletePath("user.nonexistent"));
        Assert.That(ex.Message, Does.Contain("does not exist"));
    }

    [Test]
    public void DeletePath_EmptyPath_ShouldThrowException()
    {
        // Arrange
        var data = NodeEventData.Empty;

        // Act & Assert
        Assert.Throws<ArgumentException>(() => data.DeletePath(""));
        Assert.Throws<ArgumentException>(() => data.DeletePath(null!));
    }

    [Test]
    public void DeletePath_LastPropertyInContainer_ShouldRemoveEmptyContainer()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["user"] = new Dictionary<string, object?>
            {
                ["profile"] = new Dictionary<string, object?>
                {
                    ["name"] = "John"
                }
            }
        });

        // Act
        var result = data.DeletePath("user.profile.name");

        // Assert
        Assert.That(result.TryGetPath("user.profile", out _), Is.False);
        Assert.That(result.TryGetPath("user", out _), Is.False);
    }

    [Test]
    public void DeletePaths_MultiplePaths_ShouldRemoveAll()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["user"] = new Dictionary<string, object?>
            {
                ["name"] = "John",
                ["age"] = 30,
                ["email"] = "<EMAIL>"
            },
            ["settings"] = new Dictionary<string, object?>
            {
                ["theme"] = "dark",
                ["notifications"] = true
            }
        });

        // Act
        var result = data.DeletePaths(new[] { "user.age", "settings.notifications" });

        // Assert
        Assert.That(result.GetPath("user.name"), Is.EqualTo("John"));
        Assert.That(result.GetPath("user.email"), Is.EqualTo("<EMAIL>"));
        Assert.That(result.GetPath("settings.theme"), Is.EqualTo("dark"));
        Assert.That(result.TryGetPath("user.age", out _), Is.False);
        Assert.That(result.TryGetPath("settings.notifications", out _), Is.False);
    }

    [Test]
    public void NodeEvent_DeletePath_ShouldReturnNewNodeEventWithDeletedPath()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["user"] = new Dictionary<string, object?>
            {
                ["name"] = "John",
                ["age"] = 30
            }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test.topic");

        // Act
        var result = nodeEvent.DeletePath("user.age");

        // Assert
        Assert.That(result.Node, Is.EqualTo(TestNodeName));
        Assert.That(result.Topic, Is.EqualTo("test.topic"));
        Assert.That(result.GetPath("user.name"), Is.EqualTo("John"));
        Assert.That(result.TryGetPath("user.age", out _), Is.False);

        // Original should be unchanged
        Assert.That(nodeEvent.GetPath("user.age"), Is.EqualTo(30));
    }

    [Test]
    public void NodeEvent_DeletePaths_ShouldReturnNewNodeEventWithDeletedPaths()
    {
        // Arrange
        var data = NodeEventData.FromDictionary(new Dictionary<string, object?>
        {
            ["user"] = new Dictionary<string, object?>
            {
                ["name"] = "John",
                ["age"] = 30,
                ["email"] = "<EMAIL>"
            }
        });
        var nodeEvent = new NodeEvent(TestNodeName, data, "test.topic");

        // Act
        var result = nodeEvent.DeletePaths(new[] { "user.age", "user.email" });

        // Assert
        Assert.That(result.Node, Is.EqualTo(TestNodeName));
        Assert.That(result.Topic, Is.EqualTo("test.topic"));
        Assert.That(result.GetPath("user.name"), Is.EqualTo("John"));
        Assert.That(result.TryGetPath("user.age", out _), Is.False);
        Assert.That(result.TryGetPath("user.email", out _), Is.False);
    }
}