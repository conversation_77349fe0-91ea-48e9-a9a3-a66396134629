TYPE "UDT_KB_CE"
VERSION : 0.1
   STRUCT
      "Counter" : DInt;
      Seq : Struct
         Msg_ID : Int;
         Msg_Step : Int;
         Msg_Stat : Int;
         KegID : Int;
         ActiveStep : Int;
         Val : Real;
      END_STRUCT;
      CE_Status : Struct
         Action : Int;   // 0:=Not active/1:=Active Empty not running/2:= Active Empty/3:= Active not Empty running/4:=Active buffer modus/5:= Active Saturation modus/6:= Active Saturated
         Status : Int;   // 0:=Ready Not execting no error / No warning/1:= Ready executing/2:=Hand/3:=Warning/4:=Error
      END_STRUCT;
   END_STRUCT;

END_TYPE

TYPE "UDT_KB_CM_FT"
VERSION : 0.1
   STRUCT
      Status : Byte;
      In_Pre : Bool;
      In_End : Bool;
      In_Fault : Bool;
      In_Puls : Bool;
      Out_Bit0 : Bool;
      Out_Bit1 : Bool;
      Out_Start : Bool;
      Out_Stop : Bool;
      Flow : Int;
      Total : Int;
   END_STRUCT;

END_TYPE

TYPE "UDT_KB_CM_AI"
VERSION : 0.1
   STRUCT
      Status : Byte;
      Value : Real;
   END_STRUCT;

END_TYPE

TYPE "UDT_KB_CM_DI"
VERSION : 0.1
   STRUCT
      Status : Byte;
      RawInput : Bool;
   END_STRUCT;

END_TYPE

TYPE "UDT_KB_CM_MTR"
VERSION : 0.1
   STRUCT
      Status : Byte;
      ReqFwd : Bool;
      ReqBwd : Bool;
      ActCurrent : Int;
      SpSpeed : Int;
      StatusWord : Word;
   END_STRUCT;

END_TYPE

TYPE "UDT_KB_CM_DQ"
VERSION : 0.1
   STRUCT
      Status : Byte;
      OutCls : Bool;
      OutOpn : Bool;
      FdbCls : Bool;
      FdbOpn : Bool;
   END_STRUCT;

END_TYPE

DATA_BLOCK "KegbridgeGeneral"
{ S7_Optimized_Access := 'FALSE' }
VERSION : 0.1
NON_RETAIN

BEGIN

END_DATA_BLOCK

DATA_BLOCK "KegbridgeCE"
{ S7_Optimized_Access := 'FALSE' }
VERSION : 0.1
NON_RETAIN
   STRUCT 
      CE_T01_Ac { ExternalAccessible := 'False'; ExternalVisible := 'False'; ExternalWritable := 'False'; S7_SetPoint := 'False'} : "UDT_KB_CE";
      CE_T01_Ca { ExternalAccessible := 'False'; ExternalVisible := 'False'; ExternalWritable := 'False'; S7_SetPoint := 'False'} : "UDT_KB_CE";
      CE_T01_HW { ExternalAccessible := 'False'; ExternalVisible := 'False'; ExternalWritable := 'False'; S7_SetPoint := 'False'} : "UDT_KB_CE";
      CE_T01_MW { ExternalAccessible := 'False'; ExternalVisible := 'False'; ExternalWritable := 'False'; S7_SetPoint := 'False'} : "UDT_KB_CE";
      CE_FH : "UDT_KB_CE";
      CE_WH : "UDT_KB_CE";
   END_STRUCT;


BEGIN

END_DATA_BLOCK

DATA_BLOCK "KegbridgeCM"
{ S7_Optimized_Access := 'FALSE' }
VERSION : 0.1
NON_RETAIN
   STRUCT 
      F1_H1_AI_1PT : "UDT_KB_CM_AI";
      F1_H1_AI_1TT : "UDT_KB_CM_AI";
      F1_H1_AI_1CS : "UDT_KB_CM_AI";
      F1_H1_DI_1FS : "UDT_KB_CM_DI";
      F1_H1_AI_2PT : "UDT_KB_CM_AI";
      F1_H1_AI_2TT : "UDT_KB_CM_AI";
      F1_H1_AI_2CS : "UDT_KB_CM_AI";
      F1_H1_DI_2FS : "UDT_KB_CM_DI";
      F1_H1_PT_PV : "UDT_KB_CM_AI";
      F1_H2_AI_1PT : "UDT_KB_CM_AI";
      F1_H2_AI_1TT : "UDT_KB_CM_AI";
      F1_H2_AI_1CS : "UDT_KB_CM_AI";
      F1_H2_DI_1FS : "UDT_KB_CM_DI";
      F1_H2_AI_2PT : "UDT_KB_CM_AI";
      F1_H2_AI_2TT : "UDT_KB_CM_AI";
      F1_H2_AI_2CS : "UDT_KB_CM_AI";
      F1_H2_AI_PT_PV : "UDT_KB_CM_AI";
      MTR_FC302 : "UDT_KB_CM_MTR";
      MS1_A_FT_A : "UDT_KB_CM_FT";
      MS1_CO2_FT_1CO2 : "UDT_KB_CM_FT";
      MS1_CO2_FT_2CO2 : "UDT_KB_CM_FT";
      MS1_SA_FT_SA : "UDT_KB_CM_FT";
      MS1_SSt_FT_SSt : "UDT_KB_CM_FT";
      MS1_St_FT_ST : "UDT_KB_CM_FT";
      MS1_W_FT_W : "UDT_KB_CM_FT";
      T01_Ac_CT : "UDT_KB_CM_AI";
      T01_Ac_Heat : "UDT_KB_CM_DQ";
      T01_Ac_LSH : "UDT_KB_CM_DI";
      T01_Ac_LSL : "UDT_KB_CM_DI";
      T01_Ac_Pump : "UDT_KB_CM_MTR";
      T01_Ac_TT : "UDT_KB_CM_AI";
      T01_Ac_YP_Ac : "UDT_KB_CM_DQ";
      T01_Ac_YP_St : "UDT_KB_CM_DQ";
      T01_Ac_YP_W : "UDT_KB_CM_DQ";
      T01_CaB_CT : "UDT_KB_CM_AI";
      T01_CaB_Heat : "UDT_KB_CM_DQ";
      T01_CaB_LSH : "UDT_KB_CM_DI";
      T01_CaB_LSL : "UDT_KB_CM_DI";
      T01_CaB_Pump : "UDT_KB_CM_MTR";
      T01_CaB_TT : "UDT_KB_CM_AI";
      T01_CaB_YP_Ca : "UDT_KB_CM_DQ";
      T01_CaB_YP_St : "UDT_KB_CM_DQ";
      T01_CaB_YP_W : "UDT_KB_CM_DQ";
      T01_HW_LSH : "UDT_KB_CM_DI";
      T01_HW_LSL : "UDT_KB_CM_DI";
      T01_HW_Pump : "UDT_KB_CM_MTR";
      T01_HW_TT : "UDT_KB_CM_AI";
      T01_HW_YP_St : "UDT_KB_CM_DQ";
      T01_HW_YP_W : "UDT_KB_CM_DQ";
      T01_MW_LSH : "UDT_KB_CM_DI";
      T01_MW_LSL : "UDT_KB_CM_DI";
      T01_MW_Pump : "UDT_KB_CM_MTR";
      T01_MW_YP_W : "UDT_KB_CM_DQ";
   END_STRUCT;


BEGIN

END_DATA_BLOCK

