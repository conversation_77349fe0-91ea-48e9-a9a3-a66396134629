using System.Collections.Generic;
using System.Linq;
using Parlot.Fluent;
using static Parlot.Fluent.Parsers;

namespace KegBridgeCore.Services.BinaryCoder;

/*
 * struct name {
 *   member: type;
 *   member2: array[34] of type
 *   member3: struct {
 *      nested_member: type
 *   }
 * }
 */

public class SimpleSchemaParser
{
    public static readonly Parser<List<BinaryStruct>> Parser;

    static SimpleSchemaParser()
    {
        // Basic parsers
        var identifier = Terms.Identifier().Then(static x => x.ToString());

        // Support dotted identifiers like "s7.word"
        var dottedIdentifier = identifier.And(ZeroOrMany(Terms.Char('.').SkipAnd(identifier)))
            .Then(static x => x.Item1 + string.Join("", x.Item2.Select(id => "." + id)));

        var integer = Terms.Integer().Then(static x => (int)x);

        // Whitespace and punctuation
        var openBrace = Terms.Char('{');
        var closeBrace = Terms.Char('}');
        var openBracket = Terms.Char('[');
        var closeBracket = Terms.Char(']');
        var colon = Terms.Char(':');
        var semicolon = Terms.Char(';');

        // Keywords
        var structKeyword = Terms.Text("struct");
        var arrayKeyword = Terms.Text("array");
        var ofKeyword = Terms.Text("of");

        // Type parsers
        var simpleType = dottedIdentifier.Then(static name => (IBinaryDecoderModel)new BinaryType { Name = name });

        // Forward declarations for recursive parsing
        var typeExpression = Deferred<IBinaryDecoderModel>();
        var structDefinition = Deferred<BinaryStruct>();

        var arrayType = arrayKeyword
            .SkipAnd(openBracket)
            .SkipAnd(integer)
            .AndSkip(closeBracket)
            .AndSkip(ofKeyword)
            .And(typeExpression)
            .Then(static x => (IBinaryDecoderModel)new BinaryArray
            {
                Size = x.Item1,
                Type = x.Item2 as IBinaryType ?? new BinaryType { Name = "unknown" }
            });

        // Property parser
        var property = identifier
            .AndSkip(colon)
            .And(typeExpression)
            .AndSkip(semicolon)
            .Then(static x => new BinaryProperty
            {
                Name = x.Item1,
                Type = x.Item2
            });

        // Named struct parser (for top-level structs)
        var namedStruct = structKeyword
            .SkipAnd(identifier)
            .AndSkip(openBrace)
            .And(ZeroOrMany(property))
            .AndSkip(closeBrace)
            .Then(static x => new BinaryStruct
            {
                Name = x.Item1,
                Properties = x.Item2.ToList()
            });

        // Set up the deferred parsers
        var structType = structKeyword
            .SkipAnd(identifier.Or(Terms.Text(""))) // Optional name for nested structs
            .AndSkip(openBrace)
            .And(ZeroOrMany(property))
            .AndSkip(closeBrace)
            .Then(static x => new BinaryStruct
            {
                Name = x.Item1,
                Properties = x.Item2.ToList()
            });

        structDefinition.Parser = structType;

        var typeExpressionParser = OneOf(arrayType, structDefinition.Then(static x => (IBinaryDecoderModel)x), simpleType);
        typeExpression.Parser = typeExpressionParser;

        // Schema parser (multiple structs)
        Parser = ZeroOrMany(namedStruct)
            .Then(static structs => structs.ToList());
    }

    public static List<BinaryStruct> Parse(string schema)
    {
        try
        {
            var normalizedInput = schema.Replace("\r\n", "\n");
            var parseResult = Parser.Parse(normalizedInput);
            return parseResult ?? new List<BinaryStruct>();
        }
        catch
        {
            return new List<BinaryStruct>();
        }
    }
}