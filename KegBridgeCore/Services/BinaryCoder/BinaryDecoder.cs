using System;
using System.Collections;
using System.IO;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Dynamic;
using Parlot.Fluent;

namespace KegBridgeCore.Services.BinaryCoder;

// public class PlcBase
// {
//     public string Name { get; set; }
//     public JObject Struct { get; set; }
//
//     public static JObject Parse(IEnumerator<string> lines)
//     {
//         JObject result = new JObject();
//
//         while (lines.MoveNext())
//         {
//             PlcType plcType = PlcType.Build(lines);
//             if( plcType != null )
//                 result.Add(plcType.Name,plcType.Struct);
//
//             if (lines.Current == null)
//                 break;
//
//             PlcDataBlock plcDataBlock = PlcDataBlock.Build(lines);
//             if( plcDataBlock != null )
//                 result.Add(plcDataBlock.Name, plcDataBlock.Struct);
//         }
//
//         return result;
//     }
//
//     public static JObject BuildStruct(IEnumerator<string> lines)
//     {
//         Regex startStructRegex = new Regex("\\s*STRUCT\\s*",RegexOptions.IgnoreCase);
//         Regex endStructRegex = new Regex("^\\s*END_STRUCT;\\s*$");
//         Regex memberRegex = new Regex("^\\s*(?<name>[\"\\w\\(\\)]*)(?<attrs>\\s+{.*})?\\s+:\\s+(?<type>[\"\\w]*);?.*$");
//         JObject result = null;
//
//         var matchStartType = startStructRegex.Match(lines.Current);
//         if (matchStartType.Success)
//         {
//             result = new JObject();
//
//             while (lines.MoveNext())
//             {
//                 var matchEndStruct = endStructRegex.Match(lines.Current);
//                 if (matchEndStruct.Success)
//                     break;
//
//                 var matchMember = memberRegex.Match(lines.Current);
//                 if ( matchMember.Success)
//                 {
//                     var attr = matchMember.Groups[1].Value.Replace("\"", "").Replace("(","_").Replace(")", "_");
//                     var type = matchMember.Groups[3].Value.Replace("\"", "");
//
//                     JToken attrType = null;
//                     switch (type)
//                     {
//                         case "Byte":
//                             attrType = (JValue)"s7.byte";
//                             break;
//                         case "Real":
//                             attrType = (JValue)"s7.real";
//                             break;
//                         case "Bool":
//                             attrType = (JValue)"s7.bool";
//                             break;
//                         case "Int":
//                             attrType = (JValue)"s7.int";
//                             break;
//                         case "Word":
//                             attrType = (JValue)"s7.word";
//                             break;
//                         case "DInt":
//                             attrType = (JValue)"s7.dint";
//                             break;
//                         case "Struct":
//                             attrType = (JObject)BuildStruct(lines);
//                             break;
//                         default:
//                             attrType = (JValue)type;
//                             break;
//                     }
//                     result.Add(attr, attrType);
//                 }
//                 else
//                 {
//                     throw new Exception($"Error parsing {lines.Current}");
//                 }
//             }
//         }
//         return result;
//     }
// }
//
// public class PlcType : PlcBase
// {
//     public static PlcType Build(IEnumerator<string> lines)
//     {
//         PlcType result = null;
//         Regex startRegex = new Regex("^\\s*TYPE\\s+\"(.*)\"\\s*$");
//         Regex endRegex = new Regex("^\\s*END_TYPE\\s*$");
//
//         var matchStartType = startRegex.Match(lines.Current);
//         if (matchStartType.Success)
//         {
//             result = new PlcType();
//             result.Name = matchStartType.Groups[1].Captures[0].Value;
//
//             while (lines.MoveNext())
//             {
//                 var plcStruct = BuildStruct(lines);
//                 if( plcStruct != null )
//                 {
//                     result.Struct = plcStruct;
//                 }
//
//                 var matchEndType = endRegex.Match(lines.Current);
//                 if (matchEndType.Success)
//                 {
//                     break;
//                 }
//             }
//         }
//
//         return result;
//     }
// }
//
// public class PlcDataBlock : PlcBase
// {
//     public static PlcDataBlock Build(IEnumerator<string> lines)
//     {
//         PlcDataBlock result = null;
//         Regex startRegex = new Regex("^\\s*DATA_BLOCK\\s+\"(.*)\"\\s*$");
//         Regex endRegex = new Regex("^\\s*END_DATA_BLOCK\\s*$");
//
//         var matchStartType = startRegex.Match(lines.Current);
//         if (matchStartType.Success)
//         {
//             result = new PlcDataBlock();
//             result.Name = matchStartType.Groups[1].Captures[0].Value;
//
//             while (lines.MoveNext())
//             {
//                 var plcStruct = BuildStruct(lines);
//                 if (plcStruct != null)
//                 {
//                     result.Struct = plcStruct;
//                 }
//
//                 var matchEndType = endRegex.Match(lines.Current);
//                 if (matchEndType.Success)
//                 {
//                     break;
//                 }
//             }
//         }
//
//         return result;
//     }
// }

public static class Schemas
{
    public static Dictionary<string, IDecoderAction> DecodeCatalog;

    public static void Initialize(List<BinaryStruct> schemas)
    {
        DecodeCatalog = new Dictionary<string, IDecoderAction>();
        ReadSchemas(schemas);
    }

    // public static void ReadPlcDbFiles(string[] db_files)
    // {
    //     if( db_files != null )
    //     {
    //         foreach (var db_file in db_files)
    //         {
    //             ParseDbLines(File.ReadLines(db_file));
    //         }
    //     }
    // }
    //
    // public static void ParseDbLines( IEnumerable<string> lines )
    // {
    //     var schemas = PlcBase.Parse(lines.GetEnumerator());
    //     ReadSchemas(schemas);
    // }
    //
    private static void ReadSchemas(List<BinaryStruct> schemas)
    {
        foreach (var schema in schemas)
        {
            DecodeCatalog[schema.Name] = new DecodeStruct(schema.Name, schema);
        }
    }
    
    public static dynamic Decode(string schema, byte[] data)
    {
        using var bs = new BinaryStreamReader(data);
        return DecodeCatalog[schema].Execute(bs);
    }
}

public interface IDecoderAction
{
    dynamic Execute(BinaryStreamReader bs);
}

public class DecodeStruct : IDecoderAction
{
    private string _schema;
    protected List<ValueTuple<string, IDecoderAction>> DecoderSequence;

    public DecodeStruct(string name, BinaryStruct schema)
    {
        _schema = name;
        DecoderSequence = new List<ValueTuple<string, IDecoderAction>>();
        BuildSequence(schema);
    }

    void BuildSequence(BinaryStruct schema)
    {
        foreach (var property in schema.Properties )
        {
            string propertyName = property.Name;

            IDecoderAction decoderAction = null;
            
            switch (property.Type)
            {
                case BinaryType binaryType:
                    decoderAction = SimpleTypeDecoderAction(binaryType.Name);
                    if (decoderAction == null)
                    {
                        decoderAction = Schemas.DecodeCatalog[binaryType.Name];
                    }
                    break;
                case BinaryStruct structType:
                    decoderAction = new DecodeStruct(structType.Name, structType);
                    break;
                case BinaryArray arrayType:
                    IDecoderAction elementDecoderAction = null;
                    elementDecoderAction = SimpleTypeDecoderAction(arrayType.Type.Name);
                    if (elementDecoderAction == null)
                    {
                        elementDecoderAction = Schemas.DecodeCatalog[arrayType.Type.Name];
                    }
                    if (elementDecoderAction == null)
                    {
                        throw new Exception($"Unknown type {arrayType.Type.Name}");
                    }
                    decoderAction = new DecodeArray(arrayType.Size, elementDecoderAction);
                    break;
                default:
                    throw new Exception($"Unknown type {property.Type}");
            }

            if (decoderAction != null)
            {
                DecoderSequence.Add((propertyName, decoderAction));
            }
            else
            {
                throw new Exception($"Unknown type {property.Type}");
            }
        }
    }

    IDecoderAction SimpleTypeDecoderAction(string type)
    {
        IDecoderAction decoderAction = null;

        switch (type.ToLower())
        {
            case "byte":
                decoderAction = new DecodeByte();
                break;
                
            // s7 types are aligned on word boundaries
            case "s7.byte":
                decoderAction = new DecodeS7Byte();
                break;
            case "s7.int":
                decoderAction = new DecodeS7Int();
                break;
            case "s7.dint":
                decoderAction = new DecodeS7DInt();
                break;
            case "s7.lint":
                decoderAction = new DecodeS7LInt();
                break;
            
            case "s7.uint":
            case "s7.word":
                decoderAction = new DecodeS7Word();
                break;

            case "s7.udint":
            case "s7.dword":
                decoderAction = new DecodeS7DWord();
                break;
            
            case "s7.ulint":
            case "s7.lword":
                decoderAction = new DecodeS7LWord();
                break;
            
            case "s7.real":
                decoderAction = new DecodeS7Real();
                break;
            
            case "s7.string":
                decoderAction = new DecodeS7String();
                break;
            
            case "le.dword":
                decoderAction = new DecodeLeDWord();
                break;
            
            case "le.real":
                decoderAction = new DecodeLeReal();
                break;
            
            case "s7.bit":
            case "s7.bool":
                decoderAction = new DecodeS7Bool();
                break;
        }

        return decoderAction;
    }

    public object Execute(BinaryStreamReader bs)
    {
        dynamic o = new ExpandoObject();
        var oDict = o as IDictionary<string, object>;

        if( _schema != null )
        {
            oDict["__schema"] = _schema;
        }

        foreach (var (key, action) in DecoderSequence)
        {
            object value = action.Execute(bs);
            if (!key.StartsWith("_"))
            {
                oDict[key]= value;
            }
        }

        return o;
    }
}

public class DecodeByte: IDecoderAction
{
    public object Execute(BinaryStreamReader bs)
    {
        return bs.ReadByte();
    }
}
public class DecodeS7Byte: IDecoderAction
{
    public object Execute(BinaryStreamReader bs)
    {
        bs.AlignToByte();
        return bs.ReadByte();
    }
}
public class DecodeS7Int : IDecoderAction
{
    public object Execute(BinaryStreamReader bs)
    {
        bs.AlignToWord();
        return bs.ReadS2be();
    }
}
public class DecodeS7DInt : IDecoderAction
{
    public object Execute(BinaryStreamReader bs)
    {
        bs.AlignToWord();
        return bs.ReadS4be();
    }
}
public class DecodeS7LInt : IDecoderAction
{
    public object Execute(BinaryStreamReader bs)
    {
        bs.AlignToWord();
        return bs.ReadS8be();
    }
}
public class DecodeS7Word : IDecoderAction
{
    public object Execute(BinaryStreamReader bs)
    {
        bs.AlignToWord();
        return bs.ReadU2be();
    }
}
public class DecodeS7DWord : IDecoderAction
{
    public object Execute(BinaryStreamReader bs)
    {
        bs.AlignToWord();
        return bs.ReadU4be();
    }
}
public class DecodeS7LWord : IDecoderAction
{
    public object Execute(BinaryStreamReader bs)
    {
        bs.AlignToWord();
        return bs.ReadU8be();
    }
}
public class DecodeLeDWord : IDecoderAction
{
    public object Execute(BinaryStreamReader bs)
    {
        bs.AlignToWord();
        return bs.ReadU4le();
    }
}
public class DecodeS7Real : IDecoderAction
{
    public object Execute(BinaryStreamReader bs)
    {
        bs.AlignToWord();
        return bs.ReadF4be();
        //return (float)Math.Round(f, 2);
    }
}
public class DecodeS7String : IDecoderAction
{
    public object Execute(BinaryStreamReader bs)
    {
        return bs.ReadStringS7();
        //return (float)Math.Round(f, 2);
    }
}
public class DecodeLeReal : IDecoderAction
{
    public object Execute(BinaryStreamReader bs)
    {
        bs.AlignToWord();
        return bs.ReadF4le();
        //return (float)Math.Round(f, 2);
    }
}
public class DecodeS7Bool : IDecoderAction
{
    public object Execute(BinaryStreamReader bs)
    {
        return bs.ReadBitS7();
    }
}
public class DecodeArray(int size, IDecoderAction elementDecode) : IDecoderAction
{
    public object Execute(BinaryStreamReader bs)
    {
        var array = new List<object>();
        for (int i = 0; i < size; i++)
        {
            array.Add(elementDecode.Execute(bs));
        }
        return array;
    }
}

public class Offset : IDecoderAction
{
    private int _offset;
    public Offset(int offset)
    {
        _offset = offset;
    }
    public object Execute(BinaryStreamReader bs)
    {
        bs.Seek(_offset);
        return null;
    }
}