using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using Parlot;
using Parlot.Fluent;
using static Parlot.Fluent.Parsers;

namespace KegBridgeCore.Services.BinaryCoder;

/*
 * Simplified Siemens TIA Portal DB parser built from tested components
 * 
 * Supports:
 * - TYPE definitions: TYPE "name" STRUCT ... END_STRUCT END_TYPE
 * - Simple properties: name : type;
 * - Array types: Array[0..100] of Type
 * - Nested structs: name : STRUCT ... END_STRUCT;
 * - Siemens type mapping (Bool -> bool, Byte -> byte, etc.)
 */

public class SiemensTiaDbSchemaParser
{
    // Expose individual parser components for testing
    public static readonly Parser<string> Identifier;
    public static readonly Parser<string> QuotedIdentifier;
    public static readonly Parser<string> AnyIdentifier;
    public static readonly Parser<BinaryType> SimpleType;
    public static readonly Parser<int> ArrayRange;
    public static readonly Parser<BinaryArray> ArrayType;
    public static readonly Parser<BinaryProperty> Property;
    public static readonly Parser<BinaryProperty> PropertyComplete;
    public static readonly Parser<BinaryStruct> StructType;
    public static readonly Parser<BinaryStruct> TypeDefinition;
    public static readonly Parser<BinaryStruct> DataBlockDefinition;
    public static readonly Parser<List<BinaryStruct>> Parser;

    static SiemensTiaDbSchemaParser()
    {
        // Basic parsers (all tested and working)
        Identifier = Terms.Identifier().Then(static x => x.ToString());
        QuotedIdentifier = Terms.String(StringLiteralQuotes.Double).Then(static x => x.ToString());
        AnyIdentifier = QuotedIdentifier.Or(Identifier);
        var integer = Terms.Integer().Then(static x => (int)x);
        
        // Punctuation
        var colon = Terms.Char(':');
        var semicolon = Terms.Char(';');
        var openBracket = Terms.Char('[');
        var closeBracket = Terms.Char(']');
        var openBrace = Terms.Char('{');
        var closeBrace = Terms.Char('}');
        var dot = Terms.Char('.');
        
        // Keywords
        var typeKeyword = Terms.Text("TYPE");
        var endTypeKeyword = Terms.Text("END_TYPE");
        var dataBlockKeyword = Terms.Text("DATA_BLOCK");
        var endDataBlockKeyword = Terms.Text("END_DATA_BLOCK");
        var structKeyword = Terms.Text("STRUCT");
        var endStructKeyword = Terms.Text("END_STRUCT");
        var nestedStructKeyword = Terms.Text("Struct");  // For nested structs
        var arrayKeyword = Terms.Text("Array");
        var ofKeyword = Terms.Text("of");

        // Comment parser: disabled - too complex for current Parlot setup
        var comment = Terms.Text("//")
            .Then(static _ => ""); // Just match // for now
        
        // Simple type parser - keep original Siemens type names
        SimpleType = AnyIdentifier.Then(static typeName => new BinaryType { Name = typeName });
        
        // Forward declarations for recursive parsing
        var typeExpression = Deferred<IBinaryDecoderModel>();
        var structDefinition = Deferred<BinaryStruct>();
        
        // Array range parser: [0..100] (tested)
        ArrayRange = openBracket
            .SkipAnd(integer)
            .AndSkip(dot.And(dot))
            .And(integer)
            .AndSkip(closeBracket)
            .Then(static x => x.Item2 - x.Item1 + 1); // Calculate size from range
        
        // Array type parser: Array[0..100] of Byte (tested)
        ArrayType = arrayKeyword
            .SkipAnd(ArrayRange)
            .AndSkip(ofKeyword)
            .And(typeExpression)
            .Then(static x => new BinaryArray
            {
                Size = x.Item1,
                Type = x.Item2 as IBinaryType ?? new BinaryType { Name = "unknown" }
            });
        
        // Optional property attributes parser: {InstructionName := 'Value'; S7_Visible := 'true'}
        var propertyAttributes = openBrace
            .SkipAnd(ZeroOrMany(AnyCharBefore(closeBrace)))
            .AndSkip(closeBrace)
            .Then(static _ => ""); // For now, just ignore the content

        // Property parser with optional attributes and optional comment after semicolon
        Property = AnyIdentifier
            .AndSkip(ZeroOrOne(propertyAttributes))
            .AndSkip(colon)
            .And(typeExpression)
            .AndSkip(semicolon)
            .AndSkip(ZeroOrOne(comment))  // Optional comment after semicolon
            .Then(static x => new BinaryProperty
            {
                Name = x.Item1,
                Type = x.Item2
            });

        // Property parser that ensures complete consumption (for testing) - disabled for now
        PropertyComplete = Property; // Same as Property for now
        
        // Struct definition parser for top-level structs (STRUCT...END_STRUCT)
        StructType = structKeyword
            .SkipAnd(ZeroOrMany(Property))
            .AndSkip(endStructKeyword)
            .Then(static properties => new BinaryStruct
            {
                Name = "", // Anonymous struct
                Properties = properties.ToList()
            });

        // Nested struct definition parser (Struct...END_STRUCT)
        var nestedStructType = nestedStructKeyword
            .SkipAnd(ZeroOrMany(Property))
            .AndSkip(endStructKeyword)
            .Then(static properties => new BinaryStruct
            {
                Name = "", // Anonymous nested struct
                Properties = properties.ToList()
            });
        
        // Type expression: array, struct, or simple type (tested components)
        var typeExpressionParser = OneOf<IBinaryDecoderModel>(
            ArrayType.Then(static x => (IBinaryDecoderModel)x),
            nestedStructType.Then(static x => (IBinaryDecoderModel)x),
            SimpleType.Then(static x => (IBinaryDecoderModel)x)
        );
        typeExpression.Parser = typeExpressionParser;

        // TYPE definition parser - ignores metadata between type name and STRUCT, and between END_STRUCT and END_TYPE
        TypeDefinition = typeKeyword
            .SkipAnd(AnyIdentifier)
            .AndSkip(ZeroOrMany(AnyCharBefore(structKeyword)))
            .AndSkip(structKeyword)
            .And(ZeroOrMany(Property))
            .AndSkip(endStructKeyword)
            .AndSkip(ZeroOrMany(AnyCharBefore(endTypeKeyword)))
            .AndSkip(endTypeKeyword)
            .Then(static x => new BinaryStruct
            {
                Name = x.Item1,
                Properties = x.Item2.ToList()
            });

        // DATA_BLOCK definition parser - ignores metadata between block name and STRUCT, and between END_STRUCT and END_DATA_BLOCK
        DataBlockDefinition = dataBlockKeyword
            .SkipAnd(AnyIdentifier)
            .AndSkip(ZeroOrMany(AnyCharBefore(structKeyword)))
            .AndSkip(structKeyword)
            .And(ZeroOrMany(Property))
            .AndSkip(endStructKeyword)
            .AndSkip(ZeroOrMany(AnyCharBefore(endDataBlockKeyword)))
            .AndSkip(endDataBlockKeyword)
            .Then(static x => new BinaryStruct
            {
                Name = x.Item1,
                Properties = x.Item2.ToList()
            });

        // Schema parser (multiple TYPE and DATA_BLOCK definitions)
        Parser = ZeroOrMany(OneOf<BinaryStruct>(TypeDefinition, DataBlockDefinition))
            .Then(static structs => structs.ToList());
    }
    
    /// <summary>
    /// Removes all text fragments that start with // till the end of the line from a multiline string
    /// and normalizes line endings to \n
    /// </summary>
    /// <param name="input">The input string to process</param>
    /// <returns>The string with all // comments removed</returns>
    public static string Normalize(string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        // Use regex to match // and everything after it until the end of the line
        // The pattern //.*$ matches:
        // - // literally
        // - .* any characters (except newline by default)
        // - $ end of line
        // RegexOptions.Multiline makes $ match end of each line, not just end of string
        return Regex.Replace(input.Replace("\r\n", "\n"), @"//.*$", "", RegexOptions.Multiline);
    }

    public static List<BinaryStruct> Parse(string schema)
    {
        try
        {
            var parseResult = Parser.Parse(Normalize(schema));
            return parseResult ?? new List<BinaryStruct>();
        }
        catch
        {
            return new List<BinaryStruct>();
        }
    }

}
