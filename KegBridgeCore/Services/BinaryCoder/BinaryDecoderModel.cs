using System.Collections.Generic;

namespace KegBridgeCore.Services.BinaryCoder;

public interface IBinaryDecoderModel
{
}

public interface IBinaryType
{
    public string Name { get; }
}

public class BinaryStruct : IBinaryDecoderModel, IBinaryType
{
    public string Name { get; init; }
    public List<BinaryProperty> Properties { get; init; }
}

public class BinaryArray : IBinaryDecoderModel
{
    public int Size { get; init; }
    public IBinaryType Type { get; init; }
}

public class BinaryType : IBinaryDecoderModel, IBinaryType
{
    public string Name { get; init; }
}

public class BinaryProperty : IBinaryDecoderModel
{
    public string Name { get; init; }
    public IBinaryDecoderModel Type { get; init; }   
}


